import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  flex: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  scrollContainer: {
    paddingHorizontal: 25,
    paddingTop: 10,
    paddingBottom: 24,
    flexGrow: 1,
  },
  mainLabel: {
    fontSize: 16,
    marginBottom: 8,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
    marginHorizontal: 25,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  billingLabel: {
    fontSize: 16,
    marginBottom: 8,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansMedium,
  },
  errorText: {
    color: BaseColors.errorText,
    fontSize: 13,
    fontFamily: FontFamily.OpenSansRegular,
  },
  dropdown: {
    marginBottom: 8,
  },
  footer: {
    paddingVertical: 16,
    paddingHorizontal: 25,
    backgroundColor: '#fff',
    marginBottom: 15,
  },
  addressContainer: {
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#e1e1e1',
    padding: 15,
    borderRadius: 10,
    marginVertical: 5,
  },
  subtext: {
    marginBottom: 10,
    color: BaseColors.inputColor,
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
  },
  loaderContainer: {
    justifyContent: 'center',
    flexDirection: 'column',
    flex: 1,
    alignItems: 'center',
    textAlign: 'right',
    marginHorizontal: 25,
  },
});

export default styles;
