import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { styles } from './style';
import { translate } from '@language/Translate';
import InsetWrapper from '@components/InsetWrapper';
import { BaseColors } from '@config/theme';
import TextInput from '@components/UI/TextInput';
import { isIOS } from '@app/utils/CommonFunction';
import Button from '@components/UI/Button';
import Toast from 'react-native-simple-toast';
import Header from '@components/Header';
import AuthActions from '@redux/reducers/auth/actions';
import { useAppDispatch, useAppSelector } from '@components/UseRedux';
import { getApiData } from '@app/utils/apiHelper';

interface RequestTagScreenProps {
  route: {
    params: {
      initialTagName: string;
      tagList?: string[];
      type?: 'user' | 'job';
      onTagListChange?: (tagList: string[]) => void;
    };
  };
}

interface TabProps {
  id: number;
  name: string;
  status: string;
  createdAt: string;
}


export default function RequestTagScreen({ route }: RequestTagScreenProps) {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { setUserData } = AuthActions;
  //   const { setCustomTag } = UserConfigActions;
  const { initialTagName, tagList = [], type = 'user', onTagListChange } = route.params;
  const { userData } = useAppSelector((state: any) => state.auth);
//   const { customTags } = useAppSelector((state: any) => state.userConfig);
  const customSkills = userData?.customSkills || [];

  const [loading, setLoading] = useState(false);
  const [tagName, setTagName] = useState(initialTagName);
  const [showInfo, setShowInfo] = useState(false); // <-- for tooltip toggle


  useEffect(() => {
    setTagName(initialTagName);
  }, [initialTagName]);

  const handleSubmit = async () => {
    if (!tagName.trim()) {
      console.warn(translate('requestTagModal.enterTagName'));
      return;
    }
    if (type === 'job') {
      if (tagList.includes(tagName.trim())) {
        console.warn(translate('requestTagModal.tagAlreadyExists'));
        return;
      }
      const updated = [...tagList, { name: tagName.trim() }];
      onTagListChange?.(updated);
      //   setCustomTag([...customTags, { name: tagName.trim() }]);
      navigation.goBack();
    } else {
      // API Call here
      console.log('Submitting to API:', tagName);
      setLoading(true);
      try {
        const res = await getApiData({
          endpoint: 'user-svc/save-custom-skills',
          data: {
            customSkills: [
              {
                name: tagName.trim(),
              },
            ],
          },
          method: 'POST',
        });
        if (res?.status) {
          dispatch(setUserData({ ...userData, customSkills: [...res?.data] }));
          Toast.show(res?.message || translate('requestTagModalSuccess'), Toast.BOTTOM);
          navigation.goBack();
        } else {
          Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        }
      } catch (e) {
        Toast.show(e?.message || translate('err', ''), Toast.BOTTOM);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <InsetWrapper>
      <View style={{  flex: 1 , backgroundColor: BaseColors.white }}>
        <Header
          leftIcon="cross"
          leftIconSty={{ fontSize: 24 }}
          title={translate('requestTagModal.confirmRequest')}
          onLeftPress={() => navigation.goBack()}
        />
        <View style={[styles.screen, { marginBottom: isIOS() ? 30 : 0 }]}>

          <ScrollView contentContainerStyle={styles.content}>
            {/* Already requested tags */}
            {/* {customSkills?.length > 0 && (
              <View style={styles.tagSection}>
                <View style={styles.tagTitleRow}>
                  <Text style={styles.tagTitle}>{translate('requestTagModal.alreadyRequestedTags')}</Text>
                  <TouchableOpacity onPress={() => setShowInfo((prev) => !prev)} hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}>
                    <Icon name="information-outline" size={16} color={BaseColors.primary} />
                  </TouchableOpacity>
                </View>
                {showInfo ? (
                  <Text style={styles.infoTooltip}>
                    {translate('requestTagModal.alreadyRequestedTagsTooltip')}
                  </Text>
                ) : null}
                <View style={styles.tagList}>
                  {customSkills?.map((tag: TabProps, idx: number) => (
                    <View key={idx} style={styles.tag}>
                      <Text style={styles.tagText}>{tag?.name}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )} */}

            {/* Info text */}
            <Text style={styles.infoText}>{translate('requestTagModal.aboutToRequestNewTag')}</Text>

            {/* Input */}
            <TextInput
              value={tagName}
              onChange={setTagName}
              placeholder={translate('requestTagModal.enterTagNameTitle')}
              style={styles.input}
              editable={!loading}
            />
          </ScrollView>

          {/* Footer buttons */}
          <View style={styles.footer}>
            <Button type="outlined" style={styles.cancelBtn} disable={loading} onPress={() => navigation.goBack()}>
              {translate('common.cancel')}
            </Button>
            <Button type="primary" style={styles.cancelBtn} loading={loading} disable={loading} onPress={handleSubmit}>
              {translate('requestTagModal.submitRequest')}
            </Button>
          </View>
        </View>
      </View>
    </InsetWrapper>
  );
}
