import {Dimensions, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

export default StyleSheet.create({
  card: {
    backgroundColor: BaseColors.inputBackground,
    padding: 16,
    borderRadius: 8,
    // borderWidth: 1,
    borderColor: BaseColors.white20,
    marginBottom: 10,
  },
  cardRow: {
    // flexDirection: 'row',
    // alignItems: 'center',
    marginBottom: 10,
    backgroundColor: BaseColors?.inputBackground,
    padding: 10,
    borderWidth: 0.7,
    borderColor: BaseColors.borderColor,
    borderRadius: 8,
  },
  cardTitle: {
    fontSize: 13,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textColor,
  },
  cardSubtitle: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
  },
  cardDescription: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
    marginVertical: 5,
  },
  titleTxtSty: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansBold,
    marginBottom: 10,
  },
  expSty: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    marginBottom: 10,
  },
  titleStyle: {
    fontSize: 13,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.textColor,
  },
  borderStyle: {
    borderColor: BaseColors.borderColor,
    marginLeft: 10,
  },
  removeFileButton: {
    borderWidth: 1,
    borderColor: BaseColors.textGrey,
    borderRadius: 6,
    position: 'absolute',
    right: 0,
  },
  crossView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  seeMoreButton: {
    marginTop: 0,
    alignItems: 'center',
    borderTopWidth: 0.7,
    paddingTop: 5,
    borderColor: BaseColors?.borderColor,
  },
  seeMoreText: {
    color: BaseColors.primary,
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansMedium,
  },
  fileIconColor: {
    color: BaseColors.primary,
  },
  iconSty: {
    borderWidth: 1,
    padding: 5,
    borderColor: BaseColors.primary,
    borderRadius: 7,
  },
  iconViewSty: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  editIconsty: {
    borderWidth: 1,
    borderColor: BaseColors.textGrey,
    borderRadius: 6,
    position: 'absolute',
    right: 30,
    padding: 3,
  },
  iconeditViewSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  certificateMainView: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
  },
  imageView: {
    width: Dimensions.get('screen').width / 6,
    height: Dimensions.get('screen').width / 6,
    borderRadius: 10,
    borderWidth: 0.8,
    borderColor: BaseColors.borderColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  certificateTitleSty: {
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansMedium,
    color: BaseColors.textColor,
  },
  dateTxtSty: {
    fontSize: 12,
    fontFamily: FontFamily?.OpenSansRegular,
    color: BaseColors.inputColor,
    lineHeight: 20,
  },
  boldStyle: {
    fontFamily: FontFamily?.OpenSansBold,
  },
  txtViewSty: {
    marginHorizontal: 15,
    width: Dimensions.get('screen').width / 2.3,
  },
  verificationSty: {
    justifyContent: 'flex-end',
    position: 'absolute',
    right: 10,
  },
});
