import {isIOS} from '@app/utils/CommonFunction';
import {BaseColors} from '@config/theme';
import {FontFamily} from '@config/typography';
import {Dimensions, Platform, StyleSheet} from 'react-native';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  skipButton: {
    marginTop: isIOS() ? 20 : 30,
    marginRight: 16,
    alignSelf: 'flex-end',
    backgroundColor: BaseColors.activeColor,
    padding: 7,
    borderRadius: 6,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  swiperContainer: {
    backgroundColor: BaseColors.white,
  },
  flatRate: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textColor,
    alignSelf: 'center',
    paddingTop: 5,
    marginLeft: 10,
  },
  container: {
    flex: 1,
  },
  rbSheetCustomStyles: {
    wrapper: {backgroundColor: 'hsla(360, 20%,2%, 0.6)'},
    draggableIcon: {backgroundColor: '#000'},
    container: {
      backgroundColor: 'white',
      borderTopRightRadius: 20,
      borderTopLeftRadius: 20,
      height: height / 1.2,
    },
  },
  languageContainer: {
    marginTop: 15,
    marginHorizontal: 20,
  },
  languageTitle: {
    fontSize: 22,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    textAlign: 'center',
  },
  title: {
    fontSize: 18,
    color: BaseColors.titleColor,
    fontFamily: FontFamily.OpenSansSemiBold,
    // marginBottom: 10,
  },
  detailBox: {
    marginTop: 10,
    padding: 10,
    paddingHorizontal: 15,
    backgroundColor: BaseColors.inputBackground,
    borderRadius: 10,
  },
  titleConfirm: {
    fontSize: 18,
    color: BaseColors.titleColor,
    fontFamily: FontFamily.OpenSansSemiBold,
    textAlign: 'center',
  },
  languageOption: {
    height: height / 16,
    // marginBottom: 15,
    borderWidth: 1.7,
    justifyContent: 'center',
    // alignItems: 'center',
    width: width / 1.2,
    borderRadius: 6,
    marginTop: 20,
  },
  languageButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 20,
    marginBottom: 20,
    marginTop: 10,
    gap: 10,
  },
  rowSpaceBetween: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  inputHalfWidth: {
    width: '48%',
  },
  durationContainer: {
    borderColor: '#e1e1e1',
    marginBottom: 15,
    backgroundColor: '#ffff',
  },
  moreClarificationContainer: {
    // borderWidth: 1,
    borderColor: '#e1e1e1',
    paddingVertical: IOS ? 12 : 5,
    // paddingHorizontal: 15,
    backgroundColor: '#ffff',
    borderRadius: 7,
    marginVertical: 10,
  },
  moreClarificationText: {
    fontSize: 18,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textGrey,
    paddingBottom: 10,
  },
  customMsg: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textGrey,
    marginBottom: 5,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
  },
  jobdestxtSty: {
    paddingHorizontal: 10,
    fontSize: 14,
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  saleryViewSty: {
    backgroundColor: BaseColors.activeColor,
    padding: 7,
    // width: '27%',
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
  },
  saleryTXtSty: {
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansBold,
    textAlign: 'center',
  },
  center: {
    justifyContent: 'space-between',
  },
  bodyDataHistorySty: {
    marginTop: 10,
  },
  decriptionTXtSty: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    flex: 1,
    lineHeight: 18, // Set a consistent line height
    overflow: 'hidden', // Prevent overflow
    textAlign: 'justify', // Optional for better alignment
  },
  // container: {
  //   flex: 1,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   backgroundColor: '#f7f7f7',
  // },
  openButton: {
    backgroundColor: '#3498db',
    padding: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end', // Position the modal at the bottom
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 999,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  noteSty: {
    marginVertical: 5,
    borderWidth: 1,
    borderColor: BaseColors.inputBackground,
    backgroundColor: BaseColors.inputBackground,
    padding: 5,
    borderRadius: 5,
    flexDirection: 'row',
    alignSelf: 'center',
  },
  noteTxtSty: {
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 12,
  },
  briefText: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
    paddingLeft: 3,
  },
});
