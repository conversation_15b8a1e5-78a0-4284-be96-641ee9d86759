import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { translate } from '@language/Translate';
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

type BannerVariant = 'green' | 'red' | 'primary' | 'warning';

interface InfoBannerProps {
  variant?: BannerVariant;
  mainText: string;
  subTitle?: string;
  message?: string;
  actionText?: string | null;
  onActionPress?: () => void;
  subActionText?: string | null;
  onSubActionPress?: () => void;
  fromChat?: boolean;
}

/**
 * Reusable banner component supporting green, red, and primary variants.
 * Useful for displaying status messages, alerts, or inline call-to-actions.
 *
 * Example:
 *  <InfoBanner
 *    variant="green"
 *    mainText={`${translate('paymentScheduled')} ${moment().format('DD MMM YYYY')}`}
 *  />
 *
 *  <InfoBanner
 *    variant="primary"
 *    mainText={translate('verificationText')}
 *    actionText={translate('connectBank')}
 *    onActionPress={() => navigation.navigate('profilesetUpNavigator')}
 *  />
 */

const InfoBanner = ({
  variant = 'primary',
  mainText,
  subTitle = '',
  message = '',
  actionText = '',
  onActionPress,
  subActionText = '',
  onSubActionPress,
  fromChat = false,
}: InfoBannerProps) => {
  const selectedStyle = styleMap[variant] || styleMap.primary;

  return (
    <View style={[selectedStyle.container,]}>
      <View style={styles.row}>
        <View style={[styles.textContainer, { flex: fromChat ? null : 1 }]}>
          <Text style={[selectedStyle.text, styles.mainText]} numberOfLines={4}>
            {mainText}
          </Text>
          {subTitle ? (
            <Text style={styles.subText} numberOfLines={4}>
              {subTitle}
            </Text>
          ) : null}
          {message ? (
            <Text style={styles.subText} numberOfLines={4}>
              <Text style={styles.bold}>{translate('message')}:</Text> {message}
            </Text>
          ) : null}
        </View>

        <View>
          {subActionText ? (
            <TouchableOpacity
              onPress={onSubActionPress}
              activeOpacity={0.8}
              style={[styles.button, {  backgroundColor: subTitle ? selectedStyle.text.color : BaseColors.primary, marginBottom: 10 }]}
            >
              <Text style={styles.buttonText}>{subActionText}</Text>
            </TouchableOpacity>
          ) : null}
          {!!actionText &&
          <TouchableOpacity
            onPress={onActionPress}
            activeOpacity={0.8}
            style={[styles.button, {  backgroundColor: subTitle ? selectedStyle.text.color : BaseColors.primary }]}
          >
            <Text style={styles.buttonText}>{actionText}</Text>
          </TouchableOpacity>}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textContainer: {
    flex: 1,
    // width: '80%',
    paddingRight: 10,
  },
  mainText: {
    // Removed paddingRight from here
  },
  subText: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
  },
  bold: {
    fontFamily: FontFamily.OpenSansBold,
  },
  button: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    backgroundColor: BaseColors.primary,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.white,
  },
});

const styleMap = {
  green: {
    container: {
      marginHorizontal: 20,
      padding: 10,
      borderWidth: 0.6,
      borderRadius: 5,
      borderColor: BaseColors.green,
      backgroundColor: '#E8F5E9',
      marginBottom: 5,
    },
    text: {
      fontSize: 12,
      fontFamily: FontFamily.OpenSansBold,
      color: BaseColors.green,
    },
  },
  warning: {
    container: {
      marginHorizontal: 20,
      padding: 10,
      borderWidth: 0.6,
      borderRadius: 5,
      borderColor: BaseColors.yellow,
      backgroundColor: '#FFF3E0',
      marginBottom: 5,
    },
    text: {
      fontSize: 12,
      fontFamily: FontFamily.OpenSansBold,
      color: BaseColors.yellow,
    },
  },
  red: {
    container: {
      marginHorizontal: 20,
      padding: 10,
      borderWidth: 0.6,
      borderRadius: 5,
      borderColor: BaseColors.red,
      backgroundColor: '#FDECEA',
      marginBottom: 5,
    },
    text: {
      fontSize: 12,
      fontFamily: FontFamily.OpenSansBold,
      color: BaseColors.red,
    },
  },
  primary: {
    container: {
      marginHorizontal: 20,
      padding: 10,
      borderWidth: 0.6,
      borderRadius: 5,
      borderColor: BaseColors.primary,
      backgroundColor: '#EAF4FE',
      marginBottom: 5,
    },
    text: {
      fontSize: 12,
      fontFamily: FontFamily.OpenSansBold,
      color: BaseColors.primary,
    },
  },
};

export default InfoBanner;
