import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Button from './UI/Button';
import { translate } from '@language/Translate';
import StatusTag from './Tag';
import { useAppSelector } from './UseRedux';

type BankAccountProps = {
  companyName: string;
  accountType: string;
  last4: string;
  onUpdate: () => void;
  status?: string;
};

const BankAccountCard = ({
  companyName,
  accountType,
  last4,
  onUpdate,
  status = '',
}: BankAccountProps) => {
  const { userData } = useAppSelector((s) => s.auth);


  return (
    <View style={styles.container}>
      <View>
        <View style={styles.card}>
          <View style={styles.accountInfo}>
            <Text style={styles.companyName}>{companyName}</Text>
            <Text style={styles.accountType}>{`${accountType} (*${last4})`}</Text>
          </View>

          {status && <StatusTag status={String(status || userData?.bankAccountVerified)?.toLowerCase()} />}
        </View>
      </View>
      <Button
        onPress={onUpdate}
        txtStyles={{ fontSize: 14 }}
        type="primary">
        {translate('updateBankAccount')}
      </Button>
    </View>
  );
};

export default BankAccountCard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: BaseColors.white,
    // gap: 20,
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  card: {
    borderWidth: 1,
    borderRadius: 10,
    borderColor: BaseColors.borderlight,
    paddingVertical: 16,
    paddingHorizontal: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  accountInfo: {
    flex: 1,
  },
  companyName: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    marginBottom: 4,
    color: BaseColors.textBlack,
  },
  accountType: {
    fontSize: 15,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
  },
});
