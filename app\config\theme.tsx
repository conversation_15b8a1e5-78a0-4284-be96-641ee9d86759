import { Platform } from 'react-native';

const IOS = Platform.OS === 'ios';

export const BaseColors = {
  primary: '#1d559f',
  secondary: '#E3EDFB',
  primaryLight: '#67ffff',
  primaryDark: '#00a49b',
  lightskyblue: '#43414D',
  blueLight: '#e0e0e0',
  blueLightTxt: '#90a4ae90',
  blueDark: '#37474f',
  orange: '#FF5B30',
  btnBlue: '#61C9D3',
  alertRed: '#FF0B1E',
  backgroundRed: '#ffced2',
  green: '#4caf50',
  backgroundGreen: '#f6ffed',
  borderGreen: '#b7eb8f',
  backgroundBlue: '#e6f7ff',
  borderBlue: '#91d5ff',
  red: '#FF3F3F',
  btnRed: '#E92222',
  txtcolor: '#AAAAAA',
  lightGreyColor: '#DDD',
  yellow: '#CB9D00',
  lightGrey: '#6D7177',
  amber: '#ffc107',
  blackColor: '#000000',
  whiteColor: '#ffffff',
  textBlack: '#1D1D1D',
  textGrey: '#747474',
  transparentWhite: '#1D1B2745',
  placeHolderColor: '#00000099',
  CborderColor: '#CDD4D9',
  cyan: '#ffffff',
  diamond: '#302f39',
  raised: '#302f39',
  detailsHeading: '#fff',
  white10: '#00000010',
  white20: '#E5E5E5',
  white30: '#00000030',
  white40: '#00000040',
  white50: '#00000050',
  white60: '#00000060',
  white70: '#00000070',
  white80: '#00000080',
  white90: '#00000090',
  black01: '#f3f2f2',
  black10: '#ffffff10',
  black20: '#ffffff20',
  black30: '#ffffff30',
  black40: '#ffffff40',
  black50: '#ffffff50',
  black60: '#ffffff60',
  black70: '#ffffff70',
  black80: '#ffffff80',
  black90: '#ffffff90',
  black100: '#f9f9f9',
  chatInputBackground: '#F0F0F3',

  inactive: '#F1F1F1',
  lightBlack: '#F6F6F6',
  textSecondary: '#ffffff',
  backgroundColor: '#FFFFFF',
  borderColor: '#D8D8D8',
  borderLightColor: '#e1e1e1',
  lightGreyTime: '#DDD1DB',
  white: '#FFFFFF',
  black: '#00000070',
  blackLight: '#43414D',
  lightBlackBlue: '#43414D',
  lightBlack80: '#302E3A',
  cardLightBg: '#F1F1F1',
  signUpSheet: '#FFFFFF',
  text: '#000000',
  textInput: '#E5E5E5',
  repingInner: '#FAFAFA',
  titleTextColor: '#484848',
  menuBackground: '#FFFFFF',
  sheet: '#F1F1F1',
  onAir: '#E5E5E5',
  inactiveswiper: '#E8E8E8',
  textColor: '#484848',
  discriptionTextColor: '#545454',
  activeColor: '#E3EDFB',
  secondaryBule: '#2870D0',
  logoColor: '#1D1D1D',
  inputColor: '#747474',
  lightborderColor: '#eaeaea',
  secondaryColor: '#1D559F',
  dashColor: '#757373',
  inputBackground: '#f5faff',
  borderlight: '#eae7e1',
  titleColor: '#676361',
  lightTxtColor: '#747474',
  boxTxtColor: '#CACACA',
  inputBorderSty: '#ebebeb',
  disableColor: '#8eabd0',
  vecorColor: '#2469c3',
  borderBottomColor: '#dcdddf',
  textBlackChat: '#2C2D3A',
  lightskyColor: '#0066FF',
  iconColorSty: '#C4C3C2',
  dropdownTxtColor: '#000000',
  errorColor: '#FEF0F1',
  errorUpdatetxt: '#C7BDBA',
  errorText: '#d62828',
  secondaryBorder: '#B1CBE2',
  skillColor: '#f5f7fc',
  skillTxtColor: '#181A1FCC',
  starColor: '#ffc700',
  bordrColor: '#e1e1e1',
  saveButton: '#c7d5e7',
  completedColor: '#298919',
  filterBorderColor: '#e8eef5',
  dividerColor: '#BDBDBD',
  textthemeColor: '#222',
  timeColor: '#A5ACB8',
  bottomTxtColor: '#828282',
  ratingbackground: '#d9d9d9',
  lightcolor: '#f6f9ff',
};

export const DarkBaseColor: any = {
  primary: '#1D559F',
  secondary: '#C43FE8',
  yellow: '#CB9D00',
  darkBlue: '#0064FE',
  blueColor: '#2B84C7',
  amber: '#ffc107',
  lightGrey: '#6D7177',
  primaryLight: '#67ffff',
  primaryDark: '#00a49b',
  faceBookIcon: '#4267B2',
  inactive: '#DADADA',
  lightskyblue: '#E2F9FF',
  lightBlack: '#F6F6F6',
  textSecondary: '#858585',
  backgroundGrey: '#e7f0ff',
  backgroundColor: '#000000',
  primeIconBack: '#e0f9ff',
  borderColor: '#D8D8D8',
  CborderColor: '#CDD4D9',
  white: '#ffffff',
  black: '#000000',
  red: '#FF3D00',
  btnRed: '#E92222',
  cyan: '#0BBCED',
  diamond: '#B4EFFF',
  raised: '#F5F5F5',
  detailsHeading: '#6D7177',
  txtcolor: '#AAAAAA',
  white10: '#ffffff10',
  white20: '#ffffff20',
  white30: '#ffffff30',
  white40: '#ffffff40',
  white50: '#ffffff50',
  white60: '#ffffff60',
  white70: '#ffffff70',
  white80: '#ffffff80',
  white90: '#ffffff90',
  white100: '#f9f9f9',

  black01: '#f3f2f2',
  black10: '#00000010',
  black20: '#00000020',
  black30: '#00000030',
  black40: '#00000040',
  black50: '#00000050',
  black60: '#00000060',
  black70: '#00000070',
  black80: '#00000080',
  black90: '#00000090',
  blackLight: '#ffffff',
  lightBlackBlue: '#E2F9FF',
  lightBlack80: '#ffffff',
  green: '#4caf50',
  bgNeroOpacity40: '#1e1e1e3d',
  alertRed: '#FF0B1E',
  cardLightBg: '#1C1C1C',
  signUpSheet: '#000000',
  text: '#ffffff',
  textInput: '#E5E5E5',
  repingInner: '#FFFFFF1A',
  menuBackground: '#3C3C3C',
  sheet: '#3C3C3C',
  onAir: '#3C3C3C',
  inactiveswiper: '#E8E8E8',
  titleTextColor: '#484848',
  discriptionTextColor: '#545454',
  activeColor: '#E3EDFB',
  logoColor: '#1D1D1D',
  inputColor: '#747474',
  secondaryBule: '#2870D0',
  titleColor: '#676361',
  bottomTxtColor: '#828282',
  disableColor: '#8eabd0',
  dropdownTxtColor: '#000000',
  errorUpdatetxt: '#C7BDBA',
  secondaryBorder: '#B1CBE2',
  skillColor: '#f5f7fc',
  skillTxtColor: '#181A1FCC',
  starColor: '#ffc700',
  bordrColor: '#e1e1e1',
  inputBackground: '#f5faff',
  textColor: '#484848',
  completedColor: '#298919',
  filterBorderColor: '#e8eef5',
  dividerColor: '#BDBDBD',
  textthemeColor: '#222',
  timeColor: '#A5ACB8',
  ratingbackground: '#d9d9d9',
  lightcolor: '#f6f9ff',
};
export const BaseStyles = {
  shadow: {
    shadowColor: BaseColors.black40,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  roundCorner: {
    borderTopStartRadius: 10,
    borderTopEndRadius: 10,
    borderBottomStartRadius: 10,
    borderBottomEndRadius: 10,
  },
  flexCenter: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF',
  },
  onlineIndicator: {
    width: 10,
    height: 10,
    backgroundColor: '#4caf50',
    borderRadius: 5,
    marginRight: 5,
  },
  notificationIconStyle: {
    right: 20,
    // width: 40,
    // height: 40,
    // borderWidth: 1,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.primary,
  },
};

export const FontFamily = {
  regular: IOS ? 'helvetica' : 'helvetica',
  medium: IOS ? 'helvetica-medium' : 'helveticaMedium',
  bold: IOS ? 'helvetica-bold' : 'helveticaBold',
  // black: IOS ? 'Inter-Black' : 'InterBlack',
  // semiBold: IOS ? 'Inter-SemiBold' : 'InterSemiBold',
  // thin: IOS ? 'Inter-Thin' : 'InterThin',
};
