import React, { useRef, useState } from 'react';
import { Keyboard, KeyboardAvoidingView, ScrollView, Text, View } from 'react-native';
import styles from './styles';
import Header from '@components/Header';

import { translate } from '@language/Translate';
import TextInput from '@components/UI/TextInput';
import Button from '@components/UI/Button';
import Toast from 'react-native-simple-toast';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import { isEmpty } from '@app/utils/lodashFactions';
import { isIOS } from '@app/utils/CommonFunction';
import { store } from '@redux/store/configureStore';
interface ErrorState {
  err: boolean;
  txt: string;
}

export default function ContactUs({ navigation, route }: any) {
  const { params } = route;

  const userData = store.getState().auth.userData;


  const reportType = params?.reportType;
  const jobId = params?.jobId;
  const reportId = params?.reportId;
  // const { darkmode } = useSelector((state: any) => state.auth);
  const [name, setName] = useState<any>('');
  const [nameErr, setNameErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  // const [email, setEmail] = useState<string>('');
  // const [emailErr, setEmailErr] = useState<ErrorState>({
  //   err: false,
  //   txt: '',
  // });
  const [reason, setReason] = useState<string>(
    reportType === 'report' ? reportId : '',
  );
  const [reasonErr, setReasonErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [loader, setLoader] = useState<boolean>(false);

  const [discription, setDiscription] = useState<string>('');
  const [discriptionErr, setDiscriptionErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const nameRef = useRef<any>(null);
  const emailRef = useRef<any>(null);
  const reasonRef = useRef<any>(null);
  const descriptionRef = useRef<any>(null);

  const handlePost = async (type: string) => {
    // Build the newObj with a dynamic description field
    const newObj: any = {
      fullName: name || '',
      // email: email || '',
      reason: reason || '',
      description: discription || '',
      userId: userData?.id || '',
    };

    if (reportType === 'report') {
      newObj.jobId = jobId;
    } else {
      null;
    }

    try {
      const res = await getApiData({
        endpoint:
          reportType === 'report'
            ? BaseSetting.endpoints.reportJob
            : BaseSetting.endpoints.contactUs,
        method: 'POST',
        data: newObj,
      });
      console.log('🚀 ~ handlePost ~ res:', res);

      if (res?.status === true) {
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);

        navigation.goBack();
      } else {
        setLoader(false);
        Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
      }

      setLoader(false);
    } catch (err) {
      setLoader(false);
      Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };
  const validateEmailFormat = (email: string): boolean => {
    const emailRegex =
      /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return emailRegex.test(email);
  };

  const saveValidate = () => {
    let valid = true;

    // Regular expression to allow only letters and spaces for the Name field
    const nameRegex = /^[a-zA-Z\s]*$/;

    if (isEmpty(name)) {
      setNameErr({ err: true, txt: translate('nameRequired', '') });
      valid = false;
    } else {
      if (name.length < 3 || name.length > 35) {
        setNameErr({
          err: true,
          txt: translate('nameCharactersLimit', ''),
        });
        valid = false;
      } else if (!nameRegex.test(name)) {
        setNameErr({
          err: true,
          txt: translate('nameSpecoalCharacters', ''),
        });
        valid = false;
      } else {
        setNameErr({ err: false, txt: '' });
      }
    }

    // if (!email) {
    //   setEmailErr({ err: true, txt: translate('emailRequired', '') });
    //   valid = false;
    // } else if (!validateEmailFormat(email)) {
    //   setEmailErr({ err: true, txt: translate('validemailAddress', '') });
    //   valid = false;
    // } else {
    //   setEmailErr({ err: false, txt: '' });
    // }

    if (isEmpty(reason)) {
      setReasonErr({ err: true, txt: translate('reasonRequired', '') });
      valid = false;
    } else {
      if (reason.length < 3 || reason.length > 50) {
        setReasonErr({
          err: true,
          txt: translate('reasonCharacterLimit', ''),
        });
        valid = false;
      } else {
        setReasonErr({ err: false, txt: '' });
      }
    }

    if (isEmpty(discription)) {
      setDiscriptionErr({ err: true, txt: 'Discription is required' });
      valid = false;
    } else {
      if (discription.length < 3 || discription.length > 255) {
        setDiscriptionErr({
          err: true,
          txt: 'Discription should be between 3 to 255 characters',
        });
        valid = false;
      } else {
        setDiscriptionErr({ err: false, txt: '' });
      }
    }

    if (valid) {
      handlePost('submit');
    }
  };

  return (
    <View style={styles.mainContainer}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="back-arrow"
        title={translate('contactUs', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <KeyboardAvoidingView
        enabled
        style={{ flex: 1 }}
        behavior={isIOS() ? 'padding' : 'height'}>
        <ScrollView
          // keyboardDismissMode="on-drag"
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag">
          <View style={styles?.mainContainer}>
            {reportType === 'report' ? (
              <View style={styles.declinedStatus}>
                <Text style={styles.declineText}>Job ID : {reportId}</Text>
              </View>
            ) : null}

            <View style={styles?.mainView}>
              {/* <View style={styles?.marginTop}>
                <TextInput
                  ref={nameRef}
                  title={translate('name', '')}
                  value={name}
                  onChange={(value: any) => {
                    setName(value);
                    if (value.length > 0) {
                      setNameErr({ err: false, txt: '' });
                    }
                  }}
                  onSubmit={() => {
                    emailRef?.current?.focus();
                  }}
                  placeholder={translate('typeHere', '')}
                  showError={nameErr?.err}
                  errorText={nameErr?.txt}
                />
              </View> */}
              {/* <View style={styles?.marginTop}>
                <TextInput
                  ref={emailRef}
                  title={translate('Email', '')}
                  value={email}
                  onChange={(value: any) => {
                    setEmail(value);
                    if (value.length > 0) {
                      setEmailErr({ err: false, txt: '' });
                    }
                  }}
                  onSubmit={() => {
                    reasonRef?.current?.focus();
                  }}
                  placeholder={translate('typeHere', '')}
                  showError={emailErr?.err}
                  errorText={emailErr?.txt}
                />
              </View> */}
              <View style={styles?.marginTop}>
                <TextInput
                  ref={reasonRef}
                  value={reason}
                  title={translate('reason', '')}
                  onChange={(value: string) => {
                    setReason(value);
                    if (value.length > 0) {
                      setReasonErr({ err: false, txt: '' });
                    }
                  }}
                  onSubmit={() => {
                    descriptionRef?.current?.focus();
                  }}
                  placeholder={translate('enterReason', '')}
                  showError={reasonErr?.err}
                  errorText={reasonErr?.txt}
                />
              </View>
              <View style={styles?.marginTop}>
                <TextInput
                  ref={descriptionRef}
                  value={discription}
                  title={translate('Description', '')}
                  onChange={(value: string) => {
                    setDiscription(value.trimStart());
                    if (value.length > 0) {
                      setDiscriptionErr({ err: false, txt: '' });
                    }
                  }}
                  textArea={true}
                  placeholder={translate('typeHere', '')}
                  onSubmit={() => {
                    Keyboard.dismiss();
                  }}
                  showError={discriptionErr?.err}
                  errorText={discriptionErr?.txt}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <View style={styles?.btnView}>
        <Button onPress={saveValidate} loading={loader} type="text">
          {translate('sendagain', '')}
        </Button>
      </View>
    </View>
  );
}
