const actions = {
  SET_FILTER_DATA: 'userConfig/SET_FILTER_DATA',
  SET_SORT: 'userConfig/SET_SORT',
  SET_HARBOR_FILTER_DATA: 'userConfig/SET_HARBOR_FILTER_DATA',
  CLEAR_DATA: 'userConfig/CLEAR_DATA',
  SET_UPDATE_DATA: 'userConfig/SET_UPDATE_DATA',
  SET_PAYMENT_PROCESSING: 'userConfig/SET_PAYMENT_PROCESSING',
  SET_JOB_COUNTER: 'userConfig/SET_JOB_COUNTER',
  SET_GALLERY_BLINKING: 'userConfig/SET_GALLERY_BLINKING',

  setGalleryBlinking: (isBlinking: boolean) => (dispatch: any) =>
    dispatch({
      type: actions.SET_GALLERY_BLINKING,
      isBlinking,
    }),

  setFilter: (filterData: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_FILTER_DATA,
      filterData,
    }),
  setSort: (sort: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_SORT,
      sort,
    }),
  setHarborFilter: (harborFilterData: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_HARBOR_FILTER_DATA,
      harborFilterData,
    }),
  setUpdateJobData: (updateJobData: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_UPDATE_DATA,
      updateJobData,
    }),

  setPaymentProcessing: (isProcessing: boolean) => (dispatch: any) => {
    dispatch({
      type: actions.SET_PAYMENT_PROCESSING,
      isProcessing,
    });
  },

  setJobCounter: (jobCounter: any) => (dispatch: any) => {
    dispatch({
      type: actions.SET_JOB_COUNTER,
      jobCounter,
    });
  },

  clearData: () => (dispatch: any) =>
    dispatch({
      type: actions.CLEAR_DATA,
    }),
};

export default actions;
