import { StyleSheet } from 'react-native';
import { Dimensions, Platform } from 'react-native';
import { FontFamily } from '@config/typography';
import { BaseColors } from '@config/theme';
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  msgIconStyle: {
    right: 0,
    width: 40,
    height: 40,
    borderWidth: 1,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.secondry,
  },
  mainView: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  searchView: {
    borderColor: BaseColors.bordrColor,
    borderWidth: 1,
    padding: 10,
    borderRadius: 10,
  },
  skillViewSty: {
    paddingVertical: 15,
    paddingHorizontal: 10,

    borderRadius: 10,
  },
  skillTxtSty: {
    fontSize: 21,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  mVertical: {
    marginBottom: 5,
  },
  suggestionsTxtSty: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  addskillTxtSty: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },

  aboutTxtSty: {
    fontSize: 21,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  selfTxtSty: {
    fontSize: 16,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },

  marginTopSty: {
    marginTop: 15,
  },
  brifTxtSty: {
    fontSize: 14,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
  },
  aboutViewSty: {
    marginVertical: 10,
  },
  brifViewSty: {
    marginTop: 1,
  },
  dropDownView: {
    marginBottom: 10,
    marginTop: 5,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  radiousView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  insideView: {
    paddingTop: 12,
    borderColor: 'transparent',
    borderWidth: 1,
    // backgroundColor: BaseColors.inputBackground,
    width: '48%',
    // marginTop: 15,
    borderRadius: 10,
    // backgroundColor: 'pink'
  },
  insideTXt: {
    color: BaseColors.inputColor,
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 5,
    textAlign: 'center',
  },
  radiousTxtSty: {
    fontSize: 18,
    fontFamily: FontFamily.OpenSansRegular,
    // paddingBottom: 5,
    color: BaseColors.textColor,
    marginTop: 10,
  },
  dateViewSty: {
    borderColor: BaseColors.bordrColor,
    // borderWidth: 1,
    paddingTop: 12,
    borderRadius: 10,
  },
  rowSpaceBetween: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    // paddingVertical: 10,
  },
  inputHalfWidth: {
    width: '48%',
  },
  btnView: {
    width: '48%',
  },
  btnSty: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: IOS ? 0 : 20,
    marginHorizontal: 20,
  },
  textInput: {
    height: 50,
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  dropdown: {
    marginTop: 10,
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 3,
    padding: 16,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  locationText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textColor,
  },
  locationDescription: {
    fontSize: 16,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
  },
  recentTitle: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    marginVertical: 10,
    color: BaseColors.textColor,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemText: {
    marginLeft: 10,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textColor,
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textColor,
  },
  itemDescription: {
    fontSize: 14,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
  },
  moreHistory: {
    marginTop: 10,
    alignItems: 'center',
  },
  moreHistoryText: {
    fontSize: 16,
    color: BaseColors.textBlack,
    fontFamily: FontFamily.OpenSansRegular,
  },
  locationIcon: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    borderRadius: 10,
    height: 35,
    width: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rangeView: {
    flexDirection: 'row',
  },
  scrollContent: {
    backgroundColor: 'white',
    paddingBottom: 10, // Prevent content from overlapping bottom buttons
  },
  toggleViewSty: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
});
