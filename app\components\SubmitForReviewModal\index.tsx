import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface Props {
  visible: boolean;
  onClose: () => void;
  onSubmit: () => void;
}

const SubmitJobModal: React.FC<Props> = ({ visible, onClose, onSubmit }) => {
  const [showMedia, setShowMedia] = useState(true);
  const [showPayment, setShowPayment] = useState(false);

  return (
    <Modal animationType="slide" transparent visible={visible}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.title}>Submit Job for Review</Text>

          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Note */}
            <Text style={styles.label}>Note</Text>
            <TextInput
              placeholder="Type here"
              placeholderTextColor="#aaa"
              style={[styles.input, { height: 90 }]}
              multiline
            />

            {/* Add Media */}
            <View style={styles.section}>
              <TouchableOpacity
                style={styles.sectionHeader}
                onPress={() => setShowMedia(!showMedia)}
              >
                <Text style={styles.sectionTitle}>Add Media</Text>
                <Ionicons
                  name={showMedia ? 'chevron-up' : 'chevron-down'}
                  size={18}
                  color="#333"
                />
              </TouchableOpacity>
              {showMedia && (
                <View style={styles.sectionBody}>
                  <Text style={styles.helper}>
                    Images (You can add max 10 images)
                  </Text>
                  <View style={styles.imageRow}>
                    <TouchableOpacity style={styles.uploadBox}>
                      <Ionicons name="cloud-upload-outline" size={24} color="#2563eb" />
                      <Text style={styles.uploadText}>Choose Files</Text>
                    </TouchableOpacity>
                    {[1, 2].map((i) => (
                      <View key={i} style={styles.previewBox}>
                        <Ionicons name="image-outline" size={24} color="#999" />
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>

            {/* Request Additional Payment */}
            <View style={styles.section}>
              <TouchableOpacity
                style={styles.sectionHeader}
                onPress={() => setShowPayment(!showPayment)}
              >
                <Text style={styles.sectionTitle}>Request Additional Payment</Text>
                <Ionicons
                  name={showPayment ? 'chevron-up' : 'chevron-down'}
                  size={18}
                  color="#333"
                />
              </TouchableOpacity>
              {showPayment && (
                <View style={styles.sectionBody}>
                  <Text style={styles.label}>Amount</Text>
                  <TextInput
                    placeholder="Type here"
                    placeholderTextColor="#aaa"
                    keyboardType="numeric"
                    style={styles.input}
                  />

                  <Text style={styles.label}>Description</Text>
                  <TextInput
                    placeholder="Type here"
                    placeholderTextColor="#aaa"
                    style={[styles.input, { height: 80 }]}
                    multiline
                  />
                </View>
              )}
            </View>
          </ScrollView>

          {/* Footer */}
          <View style={styles.footer}>
            <TouchableOpacity style={styles.cancelBtn} onPress={onClose}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.submitBtn} onPress={onSubmit}>
              <Text style={styles.submitText}>Submit</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default SubmitJobModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    maxHeight: '90%',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
    color: '#111',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#444',
    marginBottom: 6,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 10,
    fontSize: 14,
    marginBottom: 14,
    color: '#111',
    backgroundColor: '#f9fafb',
  },
  section: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: '#f1f5f9',
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: '#222',
  },
  sectionBody: {
    padding: 12,
  },
  helper: {
    fontSize: 12,
    color: '#666',
    marginBottom: 10,
  },
  imageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  uploadBox: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
    backgroundColor: '#f9fafb',
  },
  uploadText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  previewBox: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
    backgroundColor: '#f9fafb',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  cancelBtn: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#2563eb',
    borderRadius: 12,
    padding: 12,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelText: {
    color: '#2563eb',
    fontWeight: '600',
  },
  submitBtn: {
    flex: 1,
    backgroundColor: '#2563eb',
    borderRadius: 12,
    padding: 12,
    marginLeft: 8,
    alignItems: 'center',
  },
  submitText: {
    color: '#fff',
    fontWeight: '600',
  },
});
