import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { translate } from '@language/Translate';
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';

type PaymentStatusCardProps = {
  status: string;
  isSeeker: boolean;
  fundInProcess?: boolean;
  fundTransferDate?: string | number;
};

function normalizeDate(input?: string | number): Date | null {
  if (!input) return null;

  try {
    let timestamp: number;

    if (typeof input === 'number') {
      // If number is less than 10^12, treat it as seconds
      timestamp = input < 1e12 ? input * 1000 : input;
    } else if (!isNaN(Number(input))) {
      const num = Number(input);
      timestamp = num < 1e12 ? num * 1000 : num;
    } else {
      // Try parsing as ISO or other date string
      const date = new Date(input);
      return isNaN(date.getTime()) ? null : date;
    }

    const date = new Date(timestamp);
    return isNaN(date.getTime()) ? null : date;
  } catch {
    return null;
  }
}

const PaymentStatusCard: React.FC<PaymentStatusCardProps> = ({
  status,
  isSeeker,
  fundInProcess,
  fundTransferDate,
}) => {
  const normalizedStatus = status?.toLowerCase();
  const transferDate = normalizeDate(fundTransferDate);

  if (!isSeeker || normalizedStatus !== 'completed') {return null;}

  if (fundInProcess) {
    return (
      <View style={[styles.card, styles.processingCard]}>
        <View style={styles.row}>
          <View style={styles.iconCircleInfo} />
          <View style={styles.textContainer}>
            <Text style={styles.titleProcessing}>{translate('paymentProcessing')}</Text>
            <Text style={styles.description}>
              {translate('paymentProcessingText')}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  if (transferDate) {
    const formattedDate = transferDate?.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    return (
      <View style={[styles.card, styles.scheduledCard]}>
        <View style={styles.row}>
          <View style={styles.iconCircleSuccess} />
          <View style={styles.textContainer}>
            <Text style={styles.titleScheduled}>{translate('paymentScheduled')}</Text>
            <Text style={styles.description}>
              {/* <Text style={styles.calendarIcon}>📅 </Text> */}
              {translate('fundReceived')}{' '}
              <Text style={styles.boldText}>{formattedDate}</Text>.
            </Text>
          </View>
        </View>
      </View>
    );
  }

  return null;
};

type Styles = {
  card: ViewStyle;
  processingCard: ViewStyle;
  scheduledCard: ViewStyle;
  row: ViewStyle;
  textContainer: ViewStyle;
  titleProcessing: TextStyle;
  titleScheduled: TextStyle;
  description: TextStyle;
  boldText: TextStyle;
  iconCircleInfo: ViewStyle;
  iconCircleSuccess: ViewStyle;
  calendarIcon: TextStyle;
};

const styles = StyleSheet.create<Styles>({
  card: {
    borderRadius: 12,
    marginBottom: 10,
    padding: 16,
    marginHorizontal: 15,
  },
  processingCard: {
    backgroundColor: BaseColors.backgroundBlue,
    borderColor: BaseColors.borderBlue,
    borderWidth: 1,
  },
  scheduledCard: {
    backgroundColor: BaseColors.backgroundGreen,
    borderColor:  BaseColors.borderGreen,
    borderWidth: 1,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  textContainer: {
    flex: 1,
    paddingLeft: 12,
  },
  titleProcessing: {
    fontSize: 16,
    color: '#096dd9',
    fontFamily: FontFamily.OpenSansBold,
    marginBottom: 4,
  },
  titleScheduled: {
    fontSize: 16,
    color: BaseColors.green,
    fontFamily: FontFamily.OpenSansBold,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textColor,
  },
  boldText: {
    fontWeight: 'bold',
    color: BaseColors.green,
    fontFamily: FontFamily.OpenSansBold,
  },
  iconCircleInfo: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#1890ff',
    marginTop: 4,
  },
  iconCircleSuccess: {
    width: 18,
    height: 18,
    borderRadius: 12,
    backgroundColor: '#52c41a',
    marginTop: 4,
  },
  calendarIcon: {
    color: BaseColors.green,
  },
});

export default PaymentStatusCard;
