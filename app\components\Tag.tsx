import React from 'react';
import { Text, View, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { BaseColors } from '@config/theme';
import { capitalizeFirstLetter } from '@app/utils/CommonFunction';

interface Props {
  status: string;
}


const StatusTag = ({ status }: Props) => {
  const isPending = status === 'pending';
  const isVerified = status === 'verified';
  const isFailed = status === 'failed' || status === 'unverified';

  const backgroundColor: ViewStyle['backgroundColor'] =
    isPending
      ? `${BaseColors.yellow}33` // 20% opacity
      : isVerified
        ? `${BaseColors.green}33`
        : isFailed
          ? `${BaseColors.red}33`
          : `${BaseColors.primary}10`;

  const borderColor: ViewStyle['borderColor'] =
    isPending
      ? BaseColors.yellow
      : isVerified
        ? BaseColors.green
        : isFailed
          ? BaseColors.red
          : BaseColors.primary;

  const textColor: TextStyle['color'] = borderColor;

  return (
    <View style={[styles.container, { backgroundColor, borderColor }]}>
      <Text style={[styles.text, { color: textColor }]}>
        {capitalizeFirstLetter(status)}
      </Text>
    </View>
  );
};

export default StatusTag;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 5,
    borderWidth: 1,
    alignSelf: 'flex-start',
  },
  text: {
    fontWeight: '600',
    fontSize: 13,
  },
});
