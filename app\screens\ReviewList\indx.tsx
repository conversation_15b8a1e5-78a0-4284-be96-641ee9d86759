import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  Text,
  Dimensions,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  FlatList,
  LayoutChangeEvent } from 'react-native';
import { translate } from '@language/Translate';
import Header from '@components/Header';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { BaseColors } from '@config/theme';
import Toast from 'react-native-simple-toast';
import styles from './styles';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import NoRecord from '@components/NoRecord';
import isEmpty from 'lodash/isEmpty';
import { isArray } from '@app/utils/lodashFactions';
import TabComponent from '@components/TabComponant';
import { useIsFocused } from '@react-navigation/native';
import { getTimeAgo } from '@app/utils/CommonFunction';
import ReviewRatingComponant from '@components/ReviewRatingComponant';

import AnimatedView from '@components/AnimatedView';

export default function ReviewList({ navigation, route }: any) {
  const { params } = route;
  const { type, id } = params;


  const isFocused = useIsFocused();

  const [selectedTab, setSelectedTab] = useState<'Seeker' | 'Employer'>(
    'Employer',
  );
  const [reviewList, setReviewList] = useState({
    data: [],
    pagination: { currentPage: 1, isMore: null },
  });

  const { data, pagination } = reviewList;
  const [expandedReviews, setExpandedReviews] = useState<{
    [key: string]: boolean;
  }>({});
  const [isTruncated, setIsTruncated] = useState<{ [key: string]: boolean }>({});

  const lineHeight = 20; // Replace this with your actual line height from styles

  const toggleExpansion = (reviewId: string | number) => {
    setExpandedReviews(prevState => ({
      ...prevState,
      [reviewId]: !prevState[reviewId],
    }));
  };

  const handleLayout = (
    event: LayoutChangeEvent,
    reviewId: string | number,
  ) => {
    const { height } = event.nativeEvent.layout;
    const maxHeight = 3 * lineHeight; // Height for 3 lines

    setIsTruncated(prevState => ({
      ...prevState,
      [reviewId]: height > maxHeight, // Check if height exceeds 3 lines
    }));
  };

  const [state, setState] = useState({
    bottomLoading: false,
    refreshing: false,
    loader: false,
    saveLoader: false,
    applicantsLoader: false,
    confirmationModal: false,
    applicant: {},
    job: {},
  });
  const { loader, refreshing, bottomLoading } = state;

  const getReviewList = useCallback(
    async (page = 1, bottomLoader = false, refresh = false) => {
      if (bottomLoader) {
        setState((p: any) => ({ ...p, bottomLoading: true }));
      } else if (refresh) {
        setState((p: any) => ({ ...p, refreshing: true }));
      } else {
        setState((p: any) => ({ ...p, loader: true }));
      }
      try {
        const resp = await getApiData({
          endpoint: `${(type === 'OWN' ? selectedTab : type) === 'Employer'
            ? BaseSetting.endpoints.seekerReview
            : BaseSetting.endpoints.employerReview
          }/${id}`,
          method: 'GET',
          data: { id, page },
        });

        if (resp?.data && resp?.status) {
          const cData = reviewList.data;
          const list =
            !isEmpty(resp?.data?.reviews) && isArray(resp?.data?.reviews)
              ? resp?.data?.reviews
              : [];
          console.log('list ===>', list);
          const data = page > 1 ? [...cData, ...list] : list;
          setReviewList((p: any) => ({
            ...p,
            data: data,
            pagination: resp,
          })); // Update user details
        } else {
          setReviewList((p: any) => ({ ...p, data: [], pagination: {} })); // Update user details
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
        setState((p: any) => ({
          ...p,
          loader: false,
          bottomLoading: false,
          refreshing: false,
        }));
      } catch (error) {
        setState((p: any) => ({
          ...p,
          loader: false,
          bottomLoading: false,
          refreshing: false,
        }));
        console.error('Error fetching list:', error);
        // Toast.show('Failed to fetch data.', Toast.SHORT);
      }
    },
    [isFocused, selectedTab],
  );

  useEffect(() => {
    getReviewList();
  }, [isFocused, selectedTab]);

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (
      isCloseToBottom &&
      reviewList?.pagination?.isMore &&
      !loader &&
      !bottomLoading
    ) {
      getReviewList(
        Number(reviewList?.pagination?.currentPage) + 1,
        true,
        false,
      );
    }
  };

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      getReviewList(1, bottomLoading, refreshing);
    }
  }, [loader]);

  const listEndLoader = () => {
    if (!loader && bottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
    return null;
  };

  const rendItem = (item: any, index: any) => {
    const reviewId = item.id || index;
    const isExpanded = expandedReviews[reviewId];
    const showSeeMore = isTruncated[reviewId];
    return (
      <ReviewRatingComponant
        review={item}
        reviewId={reviewId}
        isExpanded={isExpanded}
        showSeeMore={showSeeMore}
        toggleExpansion={toggleExpansion}
        handleLayout={handleLayout}
        getTimeAgo={getTimeAgo}
      />
      // <View style={styles?.container}>
      //   <View style={styles?.bodySty}>
      //     <View style={styles?.mainReviewSty}>
      //       <View style={styles?.mainReviewView}>
      //         <View style={styles?.mainImgView}>
      //           <View style={styles?.imageViewSty}>
      //             <FastImage
      //               source={{
      //                 uri:
      //                   item?.employer?.profilePhoto ||
      //                   item?.seeker?.profilePhoto,
      //               }}
      //               style={styles?.imgSty}
      //               resizeMode="contain"
      //             />
      //           </View>
      //           <View>
      //             <Text style={styles?.txtSty}>
      //               {(item?.employer?.firstName || item?.seeker?.firstName) +
      //                 ' ' +
      //                 (item?.employer?.lastName || item?.seeker?.lastName)}
      //             </Text>
      //           </View>
      //         </View>
      //         <View style={styles?.timeView}>
      //           <Text style={styles?.txtSty}>
      //             {getTimeAgo(item?.createdAt)}
      //           </Text>
      //         </View>
      //       </View>
      //       <View style={styles?.ratingWrapSty}>
      //         <View style={styles.textView}>
      //           <Text>
      //             {isString(item?.description) ? item?.description || '-' : '-'}
      //           </Text>
      //         </View>
      //         <View style={styles.ratingView}>
      //           <Rating
      //             type="custom"
      //             ratingColor={BaseColors.starColor}
      //             ratingBackgroundColor={BaseColors.ratingbackground}
      //             ratingCount={5}
      //             imageSize={30}
      //             readonly
      //             tintColor={BaseColors.white}
      //             startingValue={item?.rating || 0} // Set default rating to 0
      //             style={{flexDirection: 'column-reverse'}}
      //           />
      //         </View>
      //       </View>
      //     </View>
      //     {<View style={styles?.underLineSty} />}
      //   </View>
      // </View>
    );
  };

  return (
    <KeyboardAwareScrollView
      bounces={false}
      contentContainerStyle={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      enableOnAndroid={false}>
      <View style={{ backgroundColor: 'white', flex: 1 }}>
        <Header
          leftIcon="back-arrow"
          title="Review"
          onLeftPress={() => {
            navigation.goBack();
          }}
        />

        {type === 'OWN' ? (
          <TabComponent
            tabs={[
              {
                key: 'Seeker',
                label: 'Seeker',
              },
              {
                key: 'Employer',
                label: 'Employer',
              },
            ]}
            selectedTab={selectedTab}
            setSelectedTab={setSelectedTab}
            badge={null}
          />
        ) : null}

        <ScrollView
          onScroll={handleScroll}
          // scrollEventThrottle={0.5}
          style={{
            ...styles.mainView,
            // marginBottom: Dimensions.get('screen').height / 150,
          }}
          nestedScrollEnabled={true}
          contentContainerStyle={{ flexGrow: 1 }}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.primary]} // Customize refresh indicator color
              tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
            />
          }
          showsVerticalScrollIndicator={false}>
          <AnimatedView>
            {loader ? (
              <View style={styles.centerMain}>
                <ActivityIndicator color={BaseColors.primary} size={'large'} />
              </View>
            ) : (
              <>
                {type !== 'OWN' ? (
                  <View style={styles?.titleSty}>
                    <View style={{ marginLeft: 10, marginBottom: 15 }}>
                      <Text style={styles?.seekerSty}>
                        {type === 'Employer' ? 'Seeker' : 'Employer'} Review
                      </Text>
                    </View>
                  </View>
                ) : null}
                <FlatList
                  data={data || []}
                  keyExtractor={(item: any) => `${item.id}+1`}
                  renderItem={({ item }) => rendItem(item)}
                  contentContainerStyle={{
                    //   marginTop: 30,
                    marginBottom: Dimensions.get('screen').height / 9,
                  }}
                  ListEmptyComponent={
                    <View style={styles.centerMain}>
                      <NoRecord title={'noReview'} />
                    </View>
                  }
                  style={{
                    ...styles.mainView,
                  }}
                  scrollEnabled={false} // Disable FlatList's scrolling
                  ListFooterComponent={listEndLoader} // Loader when loading next page.
                />
              </>
            )}
          </AnimatedView>
        </ScrollView>
      </View>
    </KeyboardAwareScrollView>
  );
}
