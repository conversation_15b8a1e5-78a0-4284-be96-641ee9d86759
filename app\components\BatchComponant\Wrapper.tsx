import React, { useEffect, useState } from 'react';
import BatchComponent from '.';
import { useSelector } from 'react-redux';
import { useAppDispatch } from '@components/UseRedux';
import AuthAction from '@redux/reducers/auth/actions';
import NotificationAction from '@redux/reducers/notification/actions';
import { handleAvailabilityUpdate, isIOS } from '@app/utils/CommonFunction';
import { translate } from '@language/Translate';
import AlertModal from '@components/AlertModal';
import WalkthroughModal from '@components/WalkthroughModal';
import NavigationService, { navigationRef } from '@navigation/NavigationService';
import ModalComponent from '@components/Modal';

export default function BadgeWrapper() {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<any>({ badgeModal: false });
  const [modalOpen, setModalOpen] = useState<any>({
    loader: false,
    confirmationModal: false,
  });

  // For modal visible (Bank setup)
  const [modalVisible, setModalVisible] = useState(false);

  const { userData, isWalkthroughVisible, rewardModal, searchScreen } = useSelector(
    (s: any) => s.auth,
  );
  const notification = useSelector((s: any) => s.notification);

  let notiData = state.data || {};

  // Track current route name to avoid unstable dependencies
  const [currentRoute, setCurrentRoute] = useState<string>('');

  // Update current route when navigation changes
  useEffect(() => {
    const routeName = navigationRef?.current?.getCurrentRoute()?.name || '';
    setCurrentRoute(routeName);
  }, []); // Empty dependency array - we'll use navigation state listener instead

  // Listen to navigation state changes
  useEffect(() => {
    const unsubscribe = navigationRef?.current?.addListener('state', () => {
      const routeName = navigationRef?.current?.getCurrentRoute()?.name || '';
      setCurrentRoute(routeName);
    });

    return unsubscribe;
  }, []);

  // Handle walkthrough visibility logic with proper timing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    console.log('searchScreen ===>', isWalkthroughVisible, searchScreen, currentRoute, navigationRef?.current?.getCurrentRoute()?.name,
      currentRoute === 'SearchScreen' && searchScreen && navigationRef?.current?.getCurrentRoute()?.name !== 'SplashScreen');
    if (currentRoute === 'SearchScreen' && searchScreen && navigationRef?.current?.getCurrentRoute()?.name !== 'SplashScreen') {
      console.log('isWalkthroughVisible ===>', userData?.isWalkThroughCompleted === false,
        currentRoute === 'SearchScreen',
        state?.badgeModal === false,
        rewardModal?.visible === false);
      console.log('inide if ====>');
      // Use a timeout to ensure all state updates have completed
      // Increased timeout to allow reward modal to fully close before showing walkthrough
      timeoutId = setTimeout(() => {
        if (userData?.isWalkThroughCompleted === false &&
            state?.badgeModal === false &&
            rewardModal?.visible === false &&
            !isWalkthroughVisible &&
            !modalOpen?.confirmationModal) {
          dispatch(AuthAction.setWalkthroughVisible(true) as any);
        } else if (state?.badgeModal === false && rewardModal?.visible === false && !isWalkthroughVisible && !modalOpen?.confirmationModal
          && userData?.isBankVerificationMustAllow
        ) {
          setModalVisible(true);
        }
      }, 1500);
    }
    return () => clearTimeout(timeoutId);
  }, [userData?.isWalkThroughCompleted, currentRoute, state?.badgeModal, rewardModal?.visible, isWalkthroughVisible, modalOpen?.confirmationModal, searchScreen]);

  useEffect(() => {
    console.log('rewardModal ===>', rewardModal);
    if (rewardModal?.visible) {
      setState((p: any) => ({ ...p, data: rewardModal?.data }));
    }
    return () => {};
  }, [rewardModal]);

  useEffect(() => {
    if (notiData?.data?.action) {
      //?.action === 'reward') {
      try {
        const action =
          typeof notiData?.data?.action === 'string'
            ? JSON.parse(notiData?.data?.action || {})
            : notiData?.data?.action;
        if (
          action?.action === 'reward' ||
          action?.action === 'streakNotification'
        ) {
          console.log('isWalkthroughVisible1111 ===>', isWalkthroughVisible, navigationRef?.current?.getCurrentRoute()?.name, notiData?.data?.action);
          if (
            isWalkthroughVisible && navigationRef?.current?.getCurrentRoute()?.name === 'SearchScreen'
          ) {
          // Hide walkthrough temporary
            dispatch(AuthAction.setWalkthroughVisible(false) as any);
          }
          if (modalVisible) {
            setModalVisible(false);
          }
          setTimeout(() => {
            setState((p: any) => ({
              ...p,
              badgeModal: true,
              action,
              meta:
                  typeof notiData?.data?.meta === 'string'
                    ? JSON.parse(notiData?.data?.meta || '{}')
                    : notiData?.data?.meta,
            }));
          }, 500);
        }
      } catch (e) {
        console.log('e ===>', e);
      }
    }
  }, [state.data]);

  useEffect(() => {
    if (!isIOS()) {
      const nData =
        notification?.notificationType?.notification ||
        notification?.notificationType ||
        {};
      if (nData?.data?.action) {
        //?.action === 'reward') {
        try {
          const action =
            typeof notiData?.data?.action === 'string'
              ? JSON.parse(notiData?.data?.action || {})
              : notiData?.data?.action;
          if (action?.action === 'reward') {
            console.log('isWalkthroughVisible !isIOS===>', isWalkthroughVisible, navigationRef?.current?.getCurrentRoute()?.name);
            if (
              isWalkthroughVisible && navigationRef?.current?.getCurrentRoute()?.name === 'SearchScreen'
            ) {
              // Hide walkthrough temporary
              dispatch(AuthAction.setWalkthroughVisible(false) as any);
            }
            if (modalVisible) {
              setModalVisible(false);
            }
            setTimeout(() => {
              setState((p: any) => ({
                ...p,
                badgeModal: true,
                action,
                data: notiData?.data,
                meta:
                    typeof notiData?.data?.meta === 'string'
                      ? JSON.parse(notiData?.data?.meta || '{}')
                      : notiData?.data?.meta,
              }));
            }, 500);
          }
        } catch (e) {
          console.log('e ===>', e);
        }
      }
    }
  }, [notification?.notificationType]);

  const handleClose = () => {
    dispatch(AuthAction.setWalkthroughVisible(false) as any);
    dispatch(
      AuthAction.setUserData({
        ...userData,
        isWalkThroughCompleted: true,
      }) as any,
    );
    handleAvailabilityUpdate('isWalkThroughCompleted', true);
  };
  return (
    <>
      {state?.badgeModal && (
        <BatchComponent
          visible={state?.badgeModal}
          modalTitle={state?.data?.title || state?.data?.notification?.title}
          type={
            state?.action?.isUpgrade || state?.isUpgrade ? 'badge' : 'normal'
          }
          state={state}
          // modalDescription={notiData?.body || notiData?.message}
          highLightText={
            state?.data?.message || state?.data?.notification?.body
          }
          setModalVisible={() => {
            setState((p: any) => ({ ...p, badgeModal: false }));
            dispatch(
              AuthAction.setReawrdModal({ visible: false, data: {} }) as any,
            );
            dispatch(NotificationAction.setNotificationType({}) as any);
            // Remove immediate walkthrough trigger to prevent race conditions
            // The walkthrough will be handled by the useEffect with proper timing
          }}
          modalHeder={''}
        />
      )}
      <>
        {isWalkthroughVisible && (<WalkthroughModal
          visible={isWalkthroughVisible}
          onClose={(type?: string) => {
            if (type === 'postJob') {
              // if (userData?.isProfileSet === false) {
              //   dispatch(AuthAction.setWalkthroughVisible(false) as any);
              //   setTimeout(() => {
              //     setModalOpen(p => ({ ...p, confirmationModal: true }));
              //   }, 100);
              // } else {
              NavigationService?.navigate('JobPosting', {});
              handleClose();
              // }
            } else {
              handleClose();
            }
          }}
        />)}

        <AlertModal
          image
          title={translate('complete', '')}
          visible={modalOpen.confirmationModal}
          setVisible={(val: any) =>
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }))
          }
          lottieViewVisible
          btnYPress={() => {
            NavigationService?.navigate('ProfileSetUp', {});
            handleClose();
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
          }}
          loader={modalOpen?.loader}
          btnYTitle={translate('letsdo')}
          confirmation
          // completeProfile
          titlesty={{ textAlign: 'center' }}
          description={translate('postDescription', '')}
          btnNTitle={translate('maybe')}
          btnNPress={() => {
            // Close the confirmation modal and let the main useEffect handle walkthrough visibility
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
            // Don't manually trigger walkthrough - let the main useEffect handle it with proper timing
          }}
        />
      </>
      <ModalComponent visible={modalVisible} setVisible={setModalVisible} />
    </>
  );
}
