import React, { useCallback, useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import FIcon from 'react-native-vector-icons/Feather';
import EIcon from 'react-native-vector-icons/Entypo';
import DocumentPicker, { DocumentPickerResponse } from 'react-native-document-picker';
import ImageCropPicker, { Image as CropImage } from 'react-native-image-crop-picker';
import { BaseColors } from '@config/theme';
import { translate } from '@language/Translate';

interface ImagePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onResult?: (result: { type: 'file' | 'photo' | 'image', data: DocumentPickerResponse[] | CropImage }) => void;
}

const IOS = Platform.OS === 'ios';

export const ImagePickerModal: React.FC<ImagePickerModalProps> = ({
  visible,
  onClose,
  onResult,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSelectFile = useCallback(async () => {
    try {
      setIsProcessing(true);
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
      });
      onResult?.({ type: 'file', data: res });
      onClose();
    } catch (err: any) {
      if (!DocumentPicker.isCancel(err)) {
        Alert.alert('Error', err.message || 'Failed to pick file');
      }
    } finally {
      setIsProcessing(false);
    }
  }, [onResult, onClose]);

  const handleTakePhoto = useCallback(async () => {
    try {
      setIsProcessing(true);
      const res = await ImageCropPicker.openCamera({
        cropping: true,
        compressImageQuality: 0.8,
      });
      onResult?.({ type: 'photo', data: res });
      onClose();
    } catch (err: any) {
      if (err?.message !== 'User cancelled image selection') {
        Alert.alert('Error', err.message || 'Failed to take photo');
      }
    } finally {
      setIsProcessing(false);
    }
  }, [onResult, onClose]);

  const handleSelectImage = useCallback(async () => {
    try {
      setIsProcessing(true);
      const res = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: true,
        compressImageQuality: 0.8,
      });
      onResult?.({ type: 'image', data: res });
      onClose();
    } catch (err: any) {
      if (err?.message !== 'User cancelled image selection') {
        Alert.alert('Error', err.message || 'Failed to select image');
      }
    } finally {
      setIsProcessing(false);
    }
  }, [onResult, onClose]);

  return (
    <Modal
      transparent
      animationType="slide"
      visible={visible}
      onRequestClose={onClose}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* File */}
          {/* <TouchableOpacity
            onPress={handleSelectFile}
            disabled={isProcessing}
            style={[styles.option, { marginTop: IOS ? 15 : 0 }]}>
            <Icon
              name="file1"
              size={22}
              color={BaseColors.primary}
              style={styles.icon}
            />
            <Text style={styles.text}>{translate('Files', '')}</Text>
          </TouchableOpacity> */}

          {/* Camera */}
          <TouchableOpacity
            onPress={handleTakePhoto}
            disabled={isProcessing}
            style={[styles.option, { paddingVertical: 10, marginLeft: 6 }]}>
            <FIcon
              name="camera"
              size={18}
              color={BaseColors.primary}
              style={{ paddingLeft: 4 }}
            />
            <Text style={styles.text}>{translate('Camera', '')}</Text>
          </TouchableOpacity>

          {/* Photos */}
          <TouchableOpacity
            onPress={handleSelectImage}
            disabled={isProcessing}
            style={[
              styles.option,
              { paddingVertical: IOS ? 0 : 10, marginLeft: 6 },
            ]}>
            <FIcon name="image" size={18} color={BaseColors.primary} />
            <Text style={styles.text}>{translate('Photos', '')}</Text>
          </TouchableOpacity>

          {/* Cancel */}
          <TouchableOpacity
            onPress={onClose}
            disabled={isProcessing}
            style={[
              styles.option,
              {
                paddingVertical: 10,
                marginHorizontal: IOS ? 0 : 20,
                borderTopWidth: 1,
                borderTopColor: BaseColors.textInput,
              },
            ]}>
            <EIcon name="cross" size={18} color={BaseColors.primary} />
            <Text style={styles.text}>{translate('Cancel', '')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  container: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    paddingBottom: IOS ? 20 : 10,
    paddingTop: 10,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    justifyContent: 'center',
    marginVertical: 5,
  },
  icon: {
    paddingRight: 5,
  },
  text: {
    marginLeft: 15,
    color: BaseColors.primary,
  },
});
