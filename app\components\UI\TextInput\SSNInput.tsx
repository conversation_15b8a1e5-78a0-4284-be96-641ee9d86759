import React from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';
import { Controller, Control } from 'react-hook-form';
import { FontFamily } from '@config/typography';
import { BaseColors } from '@config/theme';
import { isIOS } from '@app/utils/CommonFunction';
import { translate } from '@language/Translate';

type SSNInputProps = {
  control: Control<any>;
  name: string;
  errorText?: string;
  onSubmit?: () => void;
  ref?: any;
};

const SSNInput = ({ control, name, errorText, onSubmit, ref }: SSNInputProps) => {
  const maskedPrefix = '••• - •• - ';

  return (
    <Controller
      control={control}
      name={name}
      rules={{
        required: 'SSN is required',
        pattern: {
          value: /^\d{4}$/,
          message: 'Enter exactly 4 digits',
        },
      }}
      render={({ field: { value, onChange, onBlur } }) => {
        const digitsOnly = value ?? '';
        const displayValue = maskedPrefix + digitsOnly;

        const handleChange = (text: string) => {
          const onlyDigits = text.replace(/\D/g, '').slice(0, 4);
          onChange(onlyDigits);
        };

        return (
          <View style={styles.container}>
            <Text style={styles.label}>
              {translate('ssnText')}*
            </Text>
            <Text style={styles.subtext}>
              {translate('ssnSubText')}
            </Text>

            <View
              style={[
                styles.inputWrapper,
                !!errorText && styles.errorBorder,
              ]}
            >
              <TextInput
                value={displayValue}
                onChangeText={handleChange}
                onBlur={onBlur}
                keyboardType="number-pad"
                maxLength={maskedPrefix.length + 4}
                selection={{
                  start: displayValue.length,
                  end: displayValue.length,
                }}
                style={[
                  styles.input,
                  { color: errorText ? BaseColors.errorUpdatetxt : BaseColors.inputColor },
                ]}
                returnKeyType="next"
                ref={ref}
                onSubmitEditing={onSubmit}
              />

              {/* Show fake placeholder if no digits entered */}
              {!digitsOnly && (
                <Text style={styles.placeholderText}>1234</Text>
              )}
            </View>

            {!!errorText && <Text style={styles.errorText}>{errorText}</Text>}
          </View>
        );
      }}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 5,
  },
  label: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.lightTxtColor,
    paddingBottom: 5,
  },
  subtext: {
    fontSize: 12,
    color: BaseColors.dividerColor,
    fontFamily: FontFamily.OpenSansRegular,
    marginBottom: 8,
  },
  inputWrapper: {
    backgroundColor: BaseColors.inputBackground,
    borderRadius: 15,
    borderColor: 'transparent',
    borderWidth: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    justifyContent: 'center',
    zIndex: 9,
    marginBottom: 3,
  },
  input: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    paddingLeft: 14,
    paddingTop: 0,
    paddingBottom: isIOS() ? 0 : 4,
    height: 35,
  },
  placeholderText: {
    position: 'absolute',
    left: 14 + 65, // adjust this value if needed to align placeholder after masked prefix
    top: isIOS() ? 19 : 15,
    fontSize: 14,
    color: BaseColors.errorUpdatetxt,
    fontFamily: FontFamily.OpenSansRegular,
  },
  errorText: {
    fontSize: 13,
    color: BaseColors.errorText,
    paddingTop: isIOS() ? 5 : 0,
    paddingBottom: 5,
    marginHorizontal: 4,
    fontFamily: FontFamily.OpenSansRegular,
  },
  errorBorder: {
    borderColor: BaseColors.errorColor,
    backgroundColor: '#FFF2F1',
  },
});

export default SSNInput;
