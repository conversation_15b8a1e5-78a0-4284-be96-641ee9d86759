/**
 * Common font family setting
 * - This font name will be used for all template
 * - Check more how to add more font family with url below
 * @url http://passionui.com/docs/felix-travel/theming
 */

import {Platform} from 'react-native';

const IOS = Platform.OS === 'ios';

export const FontFamily = {
  // Josefin
  // JosefinRegular: IOS ? 'JosefinSans-Regular' : 'JosefinSansRegular',
  // JosefinMedium: IOS ? 'JosefinSans-Medium' : 'JosefinSansMedium',
  // JosefinSemiBold: IOS ? 'JosefinSans-SemiBold' : 'JosefinSansSemiBold',
  // JosefinBold: IOS ? 'JosefinSans-Bold' : 'JosefinSansBold',

  // OpenSans
  OpenSansRegular: IOS ? 'OpenSans-Regular' : 'OpenSansRegular',
  OpenSansMedium: IOS ? 'OpenSans-Medium' : 'OpenSansMedium',
  OpenSansSemiBold: IOS ? 'OpenSans-SemiBold' : 'OpenSansSemiBold',
  OpenSansBold: IOS ? 'OpenSans-Bold' : 'OpenSansBold',
  OpenSansExtraBold: IOS ? 'OpenSans-ExtraBold' : 'OpenSansExtraBold',
  OpenSansLight: IOS ? 'OpenSans-Light' : 'OpenSansLight',
  OpenSansItalic: IOS ? 'OpenSans-Italic' : 'OpenSansItalic',
  OpenSansBoldItalic: IOS ? 'OpenSans-BoldItalic' : 'OpenSansBoldItalic',
  OpenSansExtraBoldItalic: IOS ? 'OpenSans-ExtraBoldItalic' : 'OpenSansExtraBoldItalic',
  OpenSansSemiBoldItalic: IOS ? 'OpenSans-SemiBoldItalic' : 'OpenSansSemiBoldItalic',
};

/**
 * Fontweight setting
 * FontsFree-Net-SF-Pro-Rounded-Medium
 * - This font weight will be used for style of screens where needed
 * - Check more how to use font weight with url below
 * @url http://passionui.com/docs/felix-travel/theming
 */
export const FontWeight = {
  thin: '100',
  ultraLight: '200',
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  heavy: '800',
  black: '900',
};
