import {Dimensions, StyleSheet} from 'react-native';
import {BaseColors} from '../../config/theme';
import {FontFamily} from '@config/typography';

const {width, height} = Dimensions.get('window');

export default StyleSheet.create({
  aboutViewSty: {
    marginTop: 20,
    // marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  row: {
    flexDirection: 'row',
  },
  selfText: {
    // Define your selfText style here
    textTransform: 'capitalize',
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansBold,
  },
  optionalText: {
    // Define your optionalText style here
    fontStyle: 'italic',
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
    color: BaseColors.lightGrey,
  },
  iconWrapper: {
    width: 25,
    height: 25,
    borderRadius: 20,
    borderColor: '#000',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#000',
  },
  briefView: {
    // Define your briefView style here
    marginTop: 1,
  },
  buttonView: {
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'flex-end',
    borderWidth: 1,
    borderRadius: 10,
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderColor: BaseColors.primary,
  },
  genText: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 14,
    color: BaseColors.primary,
  },
  briefText: {
    // Define your briefText style here
    fontSize: 12,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
    // textTransform: 'capitalize',
  },
  marginTop: {
    marginTop: 15,
    marginBottom: 5,
    // Define your marginTop style here
  },
  textAreaView: {
    backgroundColor: '#f5faff',
    borderRadius: 15,
  },
  placeholderStyle: {
    color: BaseColors.black,
  },
  textInput: {
    borderColor: BaseColors.white,
    backgroundColor: '#f5faff',
    opacity: 1,
  },

  aboutView: {
    marginTop: 0,
  },
  aboutText: {
    fontSize: 21,
    fontWeight: '400',
    color: BaseColors.textColor,
    fontFamily: FontFamily?.OpenSansMedium,
    textTransform: 'capitalize',
  },
  optionalTxt: {
    fontSize: 14,
    color: BaseColors.lightGrey,
    fontFamily: FontFamily?.OpenSansRegular,
    paddingHorizontal: 5,
    fontStyle: 'italic', // Add this line
  },
  charCountText: {
    color: BaseColors.textColor,
  },
});
