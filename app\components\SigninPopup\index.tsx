import React, { useState } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import AuthScreen from '@screens/AuthScreen';

interface SignInModalProps {
  visible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
}

const { height } = Dimensions.get('window');

const SignInModal: React.FC<SignInModalProps> = ({ visible, onClose, children }) => {
  const [authType, setAuthType] = useState<'login' | 'signup'>('login');


  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose} hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}>
            <Ionicons name="close" size={24} color={BaseColors.black} />
          </TouchableOpacity>

          {/* Render your existing AuthScreen inside modal */}
          <AuthScreen
            route={{ params: { type: authType, onClose: onClose } } as any}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContainer: {
    backgroundColor: BaseColors.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingTop: 20,
    height: height * 0.95, // almost full screen from bottom
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: -3 },
    shadowRadius: 4,
    elevation: 10,
  },
  closeButton: {
    alignSelf: 'flex-end',
    padding: 6,
  },
  title: {
    fontSize: 20,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.black,
    marginBottom: 10,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  placeholderText: {
    textAlign: 'center',
    fontSize: 16,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
  },
});

export default SignInModal;
