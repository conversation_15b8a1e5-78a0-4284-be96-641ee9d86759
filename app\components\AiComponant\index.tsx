/* eslint-disable react-native/no-inline-styles */
import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, TouchableOpacity } from 'react-native';
import { CustomIcon } from '@config/LoadIcons'; // Adjust import as per your project structure
import { BaseColors } from '@config/theme'; // Adjust import as per your project structure
import { translate } from '@language/Translate'; // Adjust import as per your project structure
import TextInput from '@components/UI/TextInput';
import styles from './styles';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import Toast from 'react-native-simple-toast';
import { isArray, isEmpty } from '@app/utils/lodashFactions';
import { detectPersonalInfo, formatDate } from '@app/utils/CommonFunction';
import moment from 'moment';
import { FontFamily } from '@config/typography';

interface AiComponantProps {
  about?: string;
  setAbout?: any;
  Review?: boolean;
  type?: string;
  selectedTags?: any;
  skillsOptions?: any;
  setSkillsOptions?: any;
  jobTitle?: any;
  location?: any;
  startDate?: any;
  endTime?: any;
  startTime?: any;
  endDate?: any;
  setDescription?: any;
  aiDescription?: any;
  setAiDescription?: any;
}

const AiComponant: React.FC<AiComponantProps> = ({
  // about,
  // setAbout,
  // Review,
  type,
  selectedTags,
  jobTitle,
  skillsOptions,
  setSkillsOptions,
  location,
  startDate,
  endTime,
  startTime,
  endDate,
  // setDescription,
  setAiDescription,
  aiDescription,
}) => {
  const formattedStartDate = startDate ? formatDate(startDate) : '';
  const formattedEndDate = endDate ? formatDate(endDate) : '';
  const formattedStartTime = startTime
    ? moment(startTime).format('hh:mm A')
    : undefined;
  const formattedEndTime = endTime
    ? moment(endTime).format('hh:mm A')
    : undefined;
  const [loader, setLoader] = useState(false);
  const getDescription = async (description: any) => {
    if (isEmpty(selectedTags)) {
      Toast.show(translate('selectSkills'), Toast.SHORT);
      return false;
    }

    const skills: any = [];
    const User_Type = type === 'user';

    isArray(selectedTags) &&
      selectedTags.map((s: any) => {
        skills.push(s.name);
      });
    const data = User_Type
      ? {
        skills: skills,
        type: type,
        description: description || '',
        location: location?.description || '',
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        startTime: formattedStartTime,
        endTime: formattedEndTime,
      }
      : {
        skills: skills,
        jobTitle: jobTitle,
        type: type,
        description: aiDescription?.description || '',
        location: location?.description || '',
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        startTime: formattedStartTime,
        endTime: formattedEndTime,
      };

    try {
      setLoader(true);
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.getDescription,
        method: 'GET',
        data: data,
      });

      if (res?.status === true) {
        const isPersonalInfo = detectPersonalInfo(res?.data?.showDescription || '', false);
        // Set the job applicant list
        setAiDescription((p: any) => ({
          ...p,
          showDescription: res?.data?.showDescription || '',
          jobDescription: res?.data?.jobDescription || '',
          description: res?.data?.showDescription || '',
          descError: isPersonalInfo,
          descErrorTxt: isPersonalInfo ? translate('chatErrorSocial', '') : '',
        }));
        setLoader(false);
        // setAbout(res?.data?.jobDescription);
      } else {
        Toast.show(res?.message, Toast.BOTTOM);

        // Show error message for unauthorized action
        // setAbout(''); // Ensure the list is empty
      }
      setLoader(false);
    } catch (err) {
      Toast.show(res?.message, Toast.BOTTOM);

      // Handle API errors
      console.error('Error fetching applicants:', err);
      setLoader(false);

      // setAbout(''); // Clear the list to prevent crashes
    }
  };

  // Using useCallback to memoize the API call for fetching skills
  const getSkillList = useCallback(async () => {
    setSkillsOptions({ ...skillsOptions, loader: true });
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.subSkillList,
        method: 'GET',
        data: {
          type: type === 'user' ? 'profile' : 'job',
        },
      });
      if (res.status) {
        await res.data.map((skl: any) => {
          skl.key = skl.id;
          skl.selectable = false;
          skl.childSkills.map((chl: any) => {
            chl.key = chl.id;
          });
        });
        setSkillsOptions({
          loader: false,
          data: res.data,
          suggestions: res?.suggestions,
        });
      }
    } catch (err) {}
  }, []);

  useEffect(() => {
    getSkillList();
  }, []);

  return (
    <View>
      <View style={styles.aboutViewSty}>
        {type === 'user' ? (
          <View style={styles.row}>
            <Text style={styles.selfText}>{translate('yourSelf', '')} </Text>
          </View>
        ) : (
          <View style={styles.aboutView}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={styles.aboutText}>
                {translate('Description', '')}
              </Text>
              {/* <Text style={styles.optionalTxt}>
                ({translate('Optional', '')})
              </Text> */}
            </View>
          </View>
        )}
      </View>

      <View style={styles.briefView}>
        {type === 'job' ? (
          // <Text style={styles.briefText}>{translate('describeJob', '')}</Text>
          <Text style={styles.briefText}>
            {translate('job.description', '')}
            <Text style={{  fontStyle: 'italic' }}><Text style={{  fontFamily: FontFamily?.OpenSansSemiBoldItalic }}>{'Tip: '}</Text>{translate('job.description2')}</Text>
          </Text>

        ) : (
          <Text style={styles.briefText}>{translate('provideBrif', '')}</Text>
        )}
      </View>

      <View style={styles.marginTop}>
        <View style={styles.textAreaView}>
          <TextInput
            value={aiDescription?.description || aiDescription}
            onChange={(value: string) => setAiDescription((p: any) => ({ ...p, description: value.trimStart() }))}
            textArea={true}
            placeholderText="Type here"
            maxLength={1000}
            placeholderStyle={styles.placeholderStyle}
            iseditable={true}
            style={[styles.textInput,
              {
                borderColor: aiDescription?.descError ? '#d62828' : '#D5DBDF',
                backgroundColor: aiDescription?.descError ? '#FFF2F1' : '#f5faff',
              }]}
            onBlur={(value: any) => {
              if (String(value?.nativeEvent?.text).trim().length <= 0) {
                return false;
              }
              if(value?.nativeEvent.text) {
                console.log('value ===>', value?.nativeEvent?.text);
                const isPersonalInfo = detectPersonalInfo(value?.nativeEvent.text, false);
                if (isPersonalInfo) {
                  return setAiDescription((p: any) => ({
                    ...p,
                    descError: true,
                    descErrorTxt: translate('chatErrorSocial', ''),
                  }));
                } else {
                  setAiDescription((p: any) => ({
                    ...p,
                    descError: false,
                    descErrorTxt: '',
                  }));
                }
              }
            }}
            showError={aiDescription?.descError}
            errorText={aiDescription?.descErrorTxt}
          />
          <View style={{  alignItems: 'flex-end', flexDirection: 'row', justifyContent: 'flex-end', marginHorizontal: 10, marginBottom: 10 }}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                getDescription('');
              }}
              style={styles.buttonView}>
              <Text style={styles.genText}>{translate('generateWithAI')}</Text>
              <View style={styles.iconWrapper}>
                {loader ? (
                  <ActivityIndicator color={BaseColors.white} />
                ) : (
                  <CustomIcon
                    name="ChatGPT-icon"
                    size={18}
                    color={BaseColors.white}
                    onPress={() => {
                      getDescription('');
                    }}
                  />
                )}
              </View>
            </TouchableOpacity>
          </View>
        </View>
        <Text style={{ ...styles.briefText, marginLeft: 5 }}>{aiDescription?.description?.length} / 1000</Text>
        {/* Word Count */}
      </View>
    </View>
  );
};

export default AiComponant;
