// We no longer use updateBaseUrl in this file
// import { updateBaseUrl } from '@config/setting';

const actions = {
  SET_USER_DATA: 'auth/SET_USER_DATA',
  SET_ACCESS_TOKEN: 'auth/SET_ACCESS_TOKEN',
  SET_SHOW_INTRO: 'auth/SET_SHOW_INTRO',
  SET_SELECTED_HOME_TAB: 'auth/SET_SELECTED_HOME_TAB',
  SET_INTERNET_CONNECTED: 'auth/SET_INTERNET_CONNECTED',
  CLEAR_DATA: 'auth/CLEAR_DATA',
  SET_DARKMODE: 'auth/SET_DARKMODE',
  SET_SELECT_POSITION: 'auth/SET_SELECT_POSITION',
  SET_WALKTHROUGH_VISIBLE: 'auth/SET_WALKTHROUGH_VISIBLE',
  SET_USER_PROFILE_DATA: 'auth/SET_USER_PROFILE_DATA',
  SET_REWARD_MODAL: 'auth/SET_REWARD_MODAL',
  SET_SEARCH_SCREEN_VISIBLE: 'auth/SET_SEARCH_SCREEN_VISIBLE',
  SET_IS_TRACK_PERMISSION: 'SET_IS_TRACK_PERMISSION',
  SET_IS_UPDATE_AVAILABLE: 'SET_IS_UPDATE_AVAILABLE',
  SET_CONFIRMATION_RESULT: 'auth/SET_CONFIRMATION_RESULT',
  SET_LOGIN_POPUP_OPEN: 'auth/SET_LOGIN_POPUP_OPEN',
  SET_LOGIN_POPUP_DEFAULT_OPEN: 'auth/SET_LOGIN_POPUP_DEFAULT_OPEN',
  SET_COMPLETE_PROFILE_POPUP: 'auth/SET_COMPLETE_PROFILE_POPUP',

  setUserData: (data: any) => {
    // We don't want to update the baseUrl from the server
    // as it would override our local environment setting
    // Comment out this code to prevent the server from changing our baseUrl
    /*
    if (data?.baseUrl) {
      updateBaseUrl(data.baseUrl);
    }
    */

    // If the data contains a baseUrl, remove it to prevent any issues
    if (data?.baseUrl) {
      console.log('Ignoring baseUrl from server:', data.baseUrl);
      // Create a new object without the baseUrl property
      const { baseUrl: _, ...dataWithoutBaseUrl } = data;
      data = dataWithoutBaseUrl;
    }

    return (dispatch: any) =>
      dispatch({
        type: actions.SET_USER_DATA,
        userData: data,
      });
  },
  setUserProfileData: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_USER_PROFILE_DATA,
        userProfileData: data,
      });
  },
  setAccessToken: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_ACCESS_TOKEN,
        accessToken: data,
      });
  },
  setConfirmationResult: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_CONFIRMATION_RESULT,
        confirmationResult: data,
      });
  },
  setShowIntro: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_SHOW_INTRO,
        showIntro: data,
      });
  },
  setSelectedHomeTab: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_SELECTED_HOME_TAB,
        selectedHomeTab: data,
      });
  },
  setInternetConnected: (data: any) => {
    return (dispatch: any) =>
      dispatch({
        type: actions.SET_INTERNET_CONNECTED,
        internetConnected: data,
      });
  },
  setDarkmode: (darkmode: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_DARKMODE,
      darkmode,
    }),
  setWalkthroughVisible: (isVisible: boolean) => (dispatch: any) => {
    dispatch({
      type: actions.SET_WALKTHROUGH_VISIBLE,
      isVisible,
    });
  },
  setIsSearchScreenVisible: (searchScreen: boolean) => (dispatch: any) => {
    dispatch({
      type: actions.SET_SEARCH_SCREEN_VISIBLE,
      searchScreen,
    });
  },

  setReawrdModal: (rewardModal: any) => (dispatch: any) => {
    dispatch({
      type: actions.SET_REWARD_MODAL,
      rewardModal,
    });
  },

  setSelectPosition: (selectPosition: any) => (dispatch: any) =>
    dispatch({
      type: actions.SET_SELECT_POSITION,
      selectPosition,
    }),
  setIsTrackPermission: (isTrackPermission: boolean) => (dispatch: any) =>
    dispatch({
      type: actions.SET_IS_TRACK_PERMISSION,
      isTrackPermission,
    }),
  setIsUpdate: (isUpdateAvailable: boolean) => (dispatch: any) =>
    dispatch({
      type: actions.SET_IS_UPDATE_AVAILABLE,
      isUpdateAvailable,
    }),
  setLoginPopupOpen: (loginPopup: boolean) => (dispatch: any) => {
    console.log('setLoginPopupOpen', loginPopup);
    return (
      dispatch({
        type: actions.SET_LOGIN_POPUP_OPEN,
        loginPopup,
      }));
  },
  setLoginPopupDefaultOpen: (defaultLoginPopup: boolean) => (dispatch: any) => (
    dispatch({
      type: actions.SET_LOGIN_POPUP_DEFAULT_OPEN,
      defaultLoginPopup,
    })),

  setCompleteProfilePopup: (showProfilePopup: boolean) => (dispatch: any) => (
    dispatch({
      type: actions.SET_COMPLETE_PROFILE_POPUP,
      showProfilePopup,
    })),
  clearData: () => (dispatch: any) =>
    dispatch({
      type: actions.CLEAR_DATA,
    }),
};

export default actions;
