import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView } from 'react-native';
import { BaseColors } from '@config/theme';
import Button from '@components/UI/Button';
import Header from '@components/Header';
import { FontFamily } from '@config/typography';
import ExpandableText from '@components/ExpandebleText';
import ImageGridView from '@components/ImageGridView';
import { endpoints } from '@config/endPoints';
import { getApiData } from '@app/utils/apiHelper';
import Toast from 'react-native-simple-toast';
import { translate } from '@language/Translate';
import Timeline from 'react-native-timeline-flatlist';
import { isArray } from '@app/utils/lodashFactions';
import { useAppSelector } from '@components/UseRedux';
import ReviewJobSubmissionCard from './ReviewJobSubmissionCard';


export default function ViewTimeline({ navigation, route }: any) {
  const { params } = route;
  const { userData } = useAppSelector((state: any) => state.auth); // Use your RootState type
  const jobDetails = params?.jobDetail;
  const finishJobRequest = jobDetails?.finish_job_request;
  console.log('jobDetails ===>', finishJobRequest);

  const isNotApprovedYet = finishJobRequest?.requested_amount_status !== 'approved';
  const isEmployer = jobDetails?.userId === userData?.id;
  const lastRequestBy = finishJobRequest?.last_request_by;
  const isEditable = lastRequestBy === (isEmployer ? 'employer' : 'seeker') && isNotApprovedYet;

  const [updates, setUpdates] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const limit = 10;

  const getTimeLines = async (pageNum = 1, isRefreshing = false) => {
    if (loading) {return;}
    setLoading(true);
    try {
      const res = await getApiData({
        endpoint: endpoints.getTimeLineForFinishJobRequest,
        method: 'GET',
        data: {
          job_id: jobDetails?.id || '16420',
          page: pageNum,
          limit: limit,
        },
      });

      if (res?.status) {
        const { items, total } = res.data;
        console.log('res.data ===>', res.data?.items);
        const data = isArray(items) ? items?.reverse() : [];

        // 🔄 Transform API into timeline items
        const formatted: any[] = data.flatMap((item: any, index: number) => {
          const finish = item.finish_request;
          const media = item.media_proof || [];

          const cntrs = isArray(item.counters) ? item.counters?.reverse() : [];
          const base = {
            time: '', // you can format created_at to hh:mm if needed
            title: finish?.user_name ?? 'Unknown User',
            description: finish?.note,
            lineColor: '#ddd',
            circleColor: BaseColors.primary,
            icon: { name: 'dot' },
            raw: {
              ...finish,
              lastCounterDetails: index === 0 ? cntrs[0] : null ,
              images: media.map((m: any) => m.url),
            },
            btnVisible: index === 0,
          };


          const counters = (cntrs || []).map((c: any) => ({
            time: '',
            title: c.user_name,
            description: c.note,
            lineColor: '#ddd',
            circleColor: data?.length === index + 1 ? BaseColors.green : BaseColors.primary,
            icon: { name: c.approved_by ? 'check' : 'clock' },
            raw: {
              ...c,
              images: [],
            },
          }));

          return [base, ...counters];
        });

        setUpdates((prev) =>
          isRefreshing ? formatted : [...prev, ...formatted]
        );

        // Pagination check
        const totalPages = Math.ceil(total / limit);
        setHasMore(pageNum < totalPages);
      } else {
        Toast.show(res?.message || translate('error'), Toast.BOTTOM);
      }
    } catch (error) {
      console.log('error', error);
    } finally {
      setLoading(false);
      if (isRefreshing) {setRefreshing(false);}
    }
  };

  useEffect(() => {
    getTimeLines(page);
  }, [page]);

  const handleRefresh = useCallback((type?: string) => {
    if (type !== 'silently') {
      setRefreshing(true);
    }
    setPage(1);
    getTimeLines(1, true);
  }, []);

  const openGallery = (index: number, images?: string[]) => {
    navigation.navigate('GalleryView', {
      type: '',
      images,
      index,
    });
  };

  const renderDetail = (rowData: any) => {
    const { title, description, raw, btnVisible } = rowData;
    const lastCounterDetails = raw?.lastCounterDetails;
    if (lastCounterDetails) {
      return (
        <ReviewJobSubmissionCard
          type={isEmployer ? 'employer' : 'seeker'}
          data={jobDetails?.finish_job_request}
          jobDetail={jobDetails}
          viewTimeLine={true}
          handleUpdate={() => {
          // getList('silently');
            handleRefresh('silently');
          }}
        />);
    }
    return (
      <View style={styles.card}>
        <Text style={styles.user}>{title}</Text>
        <View style={{ flexDirection:'row',alignItems:'center' }}>
          <Text style={styles.noteTitle}>Note: </Text><ExpandableText note={description} numberOfLines={4} />
        </View>


        {raw?.amount ? (
          <Text style={styles.amount}>
            Additional Amount: <Text style={styles.blue}>${raw.amount}</Text>
          </Text>
        ) : null}

        {raw?.images?.length > 0 && (
          <ImageGridView
            images={raw?.images}
            openGallery={(index: number) => openGallery(index, raw?.images)}
          />
        )}

        {/* Buttons */}
        {btnVisible && isEditable ?
          (
            <Button
              type="light"
              txtStyles={{ ...styles.text, color: BaseColors.primary }}
              style={styles.viewBtn}
              onPress={() => {}}
            >
            Edit
            </Button>
          )
          : btnVisible && (<View style={styles.btnRow}>
            <Button
              type="primary"
              txtStyles={styles.text}
              style={styles.approveBtn}
              onPress={() => {}}
            >
            Approve
            </Button>
            <>
              <Button
                type="light"
                txtStyles={{ ...styles.text, color: BaseColors.primary }}
                style={styles.counterBtn}
                onPress={() => {}}
              >
                Counter
              </Button>
              <Button
                type="outlined"
                style={styles.rejectBtn}
                txtStyles={styles.rejectBtnText}
                onPress={() => {}}
              >
                ✕
              </Button>
            </>
          </View>)}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Header
        leftIcon="back-arrow"
        title="View Timeline"
        onLeftPress={() => navigation.goBack()}
      />
      <Timeline
        data={updates}
        circleSize={16}
        innerCircle="icon" // or "dot"
        renderDetail={renderDetail}
        lineColor={'#ddd'}
        columnFormat="single-column-left" // force icons on left
        circleColor={BaseColors.primary}
        style={{  paddingRight: 15 }}
        separator={false}
        showTime={false}
        options={{
          refreshing,
          onRefresh: handleRefresh,
          ListFooterComponent:
            loading && !refreshing ? (
              <ActivityIndicator size="small" color={BaseColors.primary} />
            ) : null,
          onEndReached: () => {
            if (hasMore && !loading) {setPage((prev) => prev + 1);}
          },
          onEndReachedThreshold: 0.5,
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: BaseColors.white },
  card: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  noteTitle: { fontFamily: FontFamily.OpenSansBold, fontSize: 14, marginBottom: 4 },
  user: { fontFamily: FontFamily.OpenSansBold, fontSize: 16, marginBottom: 4 },
  amount: { fontFamily: FontFamily.OpenSansSemiBold, fontSize: 14, marginBottom: 8, color: BaseColors.primary },
  blue: { color: BaseColors.primary, fontFamily: FontFamily.OpenSansBold },
  btnRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8, marginTop: 8 },
  text: { fontSize: 12 },
  rejectBtnText: { color: BaseColors.red, fontSize: 14 },
  viewBtn: { backgroundColor: BaseColors.inputBackground, paddingHorizontal: 0 },
  approveBtn: { backgroundColor: BaseColors.green, paddingHorizontal: 0 },
  counterBtn: { backgroundColor: BaseColors.inputBackground, paddingHorizontal: 0 },
  rejectBtn: { borderColor: BaseColors.red, paddingHorizontal: 0 },
});
