import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Button from '@components/UI/Button';
import { FontFamily } from '@config/typography';
import { BaseColors } from '@config/theme';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ImagePickerModal } from '@components/SelectModal';
import Toast from 'react-native-simple-toast';
import { getApiData } from '@app/utils/apiHelper';
import { translate } from '@language/Translate';
import FastImage from 'react-native-fast-image';
import AuthActions from '@redux/reducers/auth/actions';
import { useAppDispatch, useAppSelector } from '@components/UseRedux';
import EmptyGallery from './Empty';
import ConfirmationModal from '@components/ConfirmationPopup';

interface GalleryProps {
  title: string;
  subtitle?: string;
  images: string[];
  fromProfile?: boolean;
}

const Gallery: React.FC<GalleryProps> = ({
  title,
  subtitle,
  images,
  fromProfile = false,
}) => {
  console.log('images ==>', images);
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { setUserData } = AuthActions;
  const { userData } = useAppSelector((state: any) => state.auth);
  const [isEditing, setIsEditing] = useState(false);
  const [imageList, setImageList] = useState<any>([]);
  const [galleryLoading, setGalleryLoading] = useState(false);
  const [removeLoading, setRemoveLoading] = useState(false);

  console.log('imageList ===>', imageList);

  const [modalVisible, setModalVisible] = useState(false);

  const [confirmationModal, setConfirmationModal] = useState(false);

  const [selectedImage, setSelectedImage] = useState<any>(null);




  const handleSubmit = async () => {
    const imageData = imageList
      .filter((file: any) => file && file?.fileName)
      .map((file: any) => file?.fileName);
    if (imageList?.length === 0 || imageData?.length === 0) {
      setIsEditing(prev => !prev);
      return;
    }
    setGalleryLoading(true);
    try {
      const res = await getApiData({
        endpoint: '/user-svc/save-profile-gallery',
        data:{ images: imageList.map((file: any) => file.fileName) },
        method: 'POST',
      });
      console.log('res: ', res);
      if (res.status) {
        // ✅ Update Redux immediately
        dispatch(
          setUserData({
            ...userData,
            galleryImages: [
              ...imageList.map((img: any) => ({
                id: img.id, // API should return ID, otherwise use fileName
                url: img.filePath,
                type: 'server',
              })),
              ...(userData?.galleryImages || []),
            ],
          })
        );
        // updateUserData(userData?.id);
        setImageList([]);
        setIsEditing(prev => !prev);
        Toast.show(res?.message, Toast.BOTTOM);
      } else {
        Toast.show(res?.message || translate('error'), Toast.BOTTOM);
      }
    } catch (err) {
      Toast.show(err?.message || translate('error'), Toast.BOTTOM);
    } finally {
      setGalleryLoading(false); // End spinner
    }
  };

  const removeImage = async (img: any) => {
    console.log('img: ', imageList, img, img?.fileName);
    if (img?.type === 'local') {
      // const galleryImages = userData?.galleryImages?.filter((ig) => ig.id != img?.id);
      // dispatch(setUserData({ ...userData, galleryImages }));
      const imgLst = imageList?.filter((ig) => ig.id != img?.id);
      setImageList(imgLst);
      return false;
    } else {
      setRemoveLoading(img?.id);
      try {
        const res = await getApiData({ endpoint: `/user-svc/gallery-image/${img?.id}`, data: {}, method: 'DELETE' });
        if (res.status) {
          const galleryImages = userData?.galleryImages?.filter((ig) => ig.id != img?.id);
          dispatch(setUserData({ ...userData, galleryImages }));
          Toast.show(res?.message, Toast.BOTTOM);
        } else {
          Toast.show(res?.message, Toast.BOTTOM);
        }
        setRemoveLoading(false);
      } catch (err) {
        Toast.show(err?.message, Toast.BOTTOM);
      } finally {
        setRemoveLoading(false); // End spinner
      }
    }
  };

  const handleUpload = async (files: any) => {
    setGalleryLoading(true);
    const fileArray = Array.isArray(files) ? files : [files];

    try {
      for (const file of fileArray) {
        const data = {
          file: {
            uri: file?.uri || file?.path, // for React Native
            type: file?.mime || file.type || 'image/jpeg',
            name: file?.filename || file.name || 'photo.jpg',
          },
        };

        const res = await getApiData({
          endpoint: '/user-svc/upload-photo-gallery',
          method: 'POST',
          data: data,
          formData: true,
        });

        if (res.status) {
          console.log('res ==>', res, res.data);
          const newLocalURL = {
            type: 'local',
            ...res.data,
            url: res.data.filePath,
            id: Math.random(),
          };
          // dispatch(
          //   setUserData({
          //     ...userData,
          //     galleryImages: [newLocalURL, ...(userData?.galleryImages || [])],
          //   })
          // );
          setImageList([...imageList, { ...newLocalURL }]);
          Toast.show(res?.message, Toast.BOTTOM);
        } else {
          console.log('Failed to upload image:', res);
          Toast.show(res?.message || translate('error'), Toast.BOTTOM);
        }
      }
    } catch (err) {
      Toast.show(err?.message || translate('error'), Toast.BOTTOM);
    } finally {
      setGalleryLoading(false);
    }
  };

  const handleFileSelect = (type?: string, data?: any) => {
    console.log('Select file', type, data);
    handleUpload(data);
    setModalVisible(false);
  };

  const toggleEditMode = () => {
    if (isEditing) {
      handleSubmit();
    } else {
      setIsEditing(prev => !prev);
    }
  };

  const openFullImage = (index: number) => {
    if (isEditing) {
      return;
    }
    navigation.navigate('GalleryView', {
      images,
      index,
      type: 'gallery',
    });
  };

  const confirmDelete = (img: any) => {
    setSelectedImage(img);
    setConfirmationModal(true);
  };

  const handleCancelDelete = () => {
    setConfirmationModal(false);
  };

  const handleConfirmDelete = async () => {
    setModalVisible(false);
    if (selectedImage) {
      await removeImage(selectedImage);
      setSelectedImage(null);
      setConfirmationModal(false);
    }
  };

  return (
    <>
      <ScrollView contentContainerStyle={styles.container}>
        {/* Header */}
        {images?.length === 0 && !isEditing ? (
          <EmptyGallery
            fromProfile={fromProfile}
            handleUpload={() => {
              setIsEditing(true);
            }}
          />
        ) : (
          <>
            <View style={styles.headerRow}>
              <View style={{ flex: 1 }}>
                <Text style={styles.title}>{title}</Text>
                {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
              </View>
              {fromProfile ? (
                <Button
                  type="outlined"
                  containerStyle={[styles.editButton]}
                  borderStyle={{ borderWidth: 0 }}
                  onPress={toggleEditMode}
                  loading={galleryLoading}>
                  {isEditing ? 'Save' : 'Edit'}
                </Button>) : null}
            </View>

            {/* Image Grid */}
            <View style={styles.grid}>
              {/* Add Button in edit mode */}
              {isEditing && (
                <TouchableOpacity
                  style={[styles.addBox]}
                  activeOpacity={0.8}
                  onPress={() => {
                    setModalVisible(true);
                  }}>
                  <Icon name="add" size={40} color={BaseColors.borderBlue} />
                </TouchableOpacity>
              )}

              {/* Newly Added Images */}
              {imageList.map((data: any, idx: number) => (
                <View key={`new-${idx}`} style={styles.imageWrapper}>
                  <FastImage
                    source={{ uri: data?.filePath, priority: FastImage.priority.high }}
                    style={styles.image}
                    resizeMode={FastImage.resizeMode.cover}
                  />
                  {isEditing && (
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => confirmDelete(data)}>
                      <Icon name="close" size={16} color="#fff" />
                    </TouchableOpacity>
                  )}
                </View>
              ))}

              {/* Existing Images */}
              {images?.map((data: any, idx) => (
                <View key={`saved-${idx}`} style={styles.imageWrapper}>
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => openFullImage(idx)}>
                    <FastImage
                      source={{ uri: data?.url, priority: FastImage.priority.high }}
                      style={styles.image}
                      resizeMode={FastImage.resizeMode.cover}
                    />
                  </TouchableOpacity>

                  {isEditing && (
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => confirmDelete(data)}>
                      {removeLoading === data?.id ? (
                        <ActivityIndicator size="small" color="#fff" />
                      ) : (
                        <Icon name="close" size={16} color="#fff" />
                      )}
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </>
        )}

      </ScrollView>
      <ImagePickerModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onResult={(props: any) => {handleFileSelect(props.type, props.data);
        }}
      />

      <View style={{ flex: 1 }}>
        {/* Confirmation Modal */}
        <ConfirmationModal
          visible={confirmationModal}
          title={translate('gallery.deleteTitle')}
          subtitle={translate('gallery.subtitle')}
          // subtitle="This action cannot be undone."
          onCancel={handleCancelDelete}
          onConfirm={handleConfirmDelete}
          confirmText={translate('gallery.delete')}
          cancelText={translate('gallery.cancel')}
          loading={removeLoading === selectedImage?.id}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
    marginTop: 10,
  },
  title: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.textBlack,
  },
  subtitle: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    marginTop: 4,
    color: BaseColors.textGrey,
  },
  editButton: {
    borderRadius: 6,
    paddingVertical: 4,
    paddingHorizontal: 10,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  imageWrapper: {
    flexBasis: '31%',
    margin: '1%',
    aspectRatio: 1,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  addBox: {
    width: '31%',        // ✅ Match image box width
    margin: '1%',
    aspectRatio: 1,      // ✅ Match image box height
    borderWidth: 1,
    borderColor: BaseColors.borderBlue,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  removeButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: BaseColors.red,
    borderRadius: 12,
    padding: 2,
    zIndex: 1,
  },
});

export default Gallery;
