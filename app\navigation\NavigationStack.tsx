/* eslint-disable react/no-unstable-nested-components */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from 'react';
import { createStackNavigator , StackCardInterpolationProps } from '@react-navigation/stack';
import {
  DefaultTheme,
  NavigationContainer,
  Theme,
} from '@react-navigation/native';
import BottomTabbar from '@navigation/BottomTabbar';
import { store } from '@redux/store/configureStore';
import { Linking, Platform } from 'react-native';
import Home from '@screens/Home';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import notificationReducer from '../redux/reducers/notification/reducer';
import ProfileScreen from '@screens/ProfileScreen';
import SplashScreen from '@screens/SplashScreen';
import { NotificationContext } from '@components';
import { navigationRef } from './NavigationService';
import { EventRegister } from 'react-native-event-listeners';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import NetInfo from '@react-native-community/netinfo';
import { BaseColors, DarkBaseColor } from '../config/theme';
import IntroScreen from '@screens/IntroScreen';
import OTPVerify from '@screens/OTPVerify';
import AdName from '@screens/AddName';
import ProfileSetUp from '@screens/ProfileSetup';
import PaymentScreen from '@screens/PaymentScreen';
import AuthScreen from '@screens/AuthScreen';
import NetworkLost from '@screens/NoInternet';
import Settings from '@screens/Settings';
import JobPosting from '@screens/JobPosting';
import WelcomeScreen from '@screens/WelcomeScreen';
import FilterScreen from '@screens/FilterScreen';
import MyHarbor from '@screens/MyHarbor';
import ReviewScreen from '@screens/ReviewScreen';
import JobApplicant from '@screens/JobApplicant';
import Applicants from '@screens/Applicants/Index';
import JobDetailScreen from '@screens/JobDetailScreen';
import ApplicantDetails from '@screens/ApplicantDetails';
import Notification from '@screens/Notification';
import { StripeProvider, useStripe } from '@stripe/stripe-react-native';
import ChatScreen from '@screens/Chat';
import ChatDetails from '@screens/ChatDetails';
import BaseSetting from '@config/setting';
import ReviewList from '@screens/ReviewList/indx';
import PrivacyPolicy from '@screens/PrivacyPolicy';
import AboutScreen from '@screens/AboutScreen';
import ContactUs from '@screens/ContactUs';
import FAQs from '@screens/FAQ';
import VerificationDetail from '@screens/VerificationDetail';
import HarborPolicy from '@screens/HarborPolicy';
import Policy from '@screens/Policy';
import RewardScreen from '@screens/RewardScreen';
import SearchScreen from '@screens/SearchScreen';
import FeaturedList from '@screens/FeaturedScreen';
import Wallet from '@screens/WalletScreen';
import KycCompleteScreen from '@components/KycCompalete';
import GalleryView from '@components/GalleryView';
import WebViewScreen from '@screens/WebView';
import HarborHistory from '@screens/HarborHistory';
import AllHistory from '@screens/AllHistory';
import EditAccount from '@screens/EditAccount';
import CounterHistory from '@screens/CounterHistory/Index';
import VideoTutorial from '@screens/VideoTutorial';
import RemotePushNotification from '@components/Common/PushNotification';
import BadgeWrapper from '@components/BatchComponant/Wrapper';
import BankDetailsForm from '@screens/BankDetails';
import RequestTagScreen from '@screens/RequestTag';
import SingInWrapper from '@components/SigninPopup/SinginWrapper';
import NewProfileCompleteModal from '@components/AlertModal/NewProfileCompleteModal';
import SubmitJobScreen from '@screens/SubmitForReview';
import ViewTimeline from '@components/SubmitForReviewModal/ViewTimeline';

const Stack = createStackNavigator();
const HomeStack = createStackNavigator();
const ProfileStack = createStackNavigator();
const StoreStack = createStackNavigator();
// const ExploreStack = createStackNavigator();
const Tab = createBottomTabNavigator();
const intitialNotificationState: any = {
  notification: null,
  openedNotification: null,
  countOfNotification: 0,
};
const IOS = Platform.OS === 'ios';

export default function NavigationStack() {
  const { handleURLCallback } = useStripe();

  const darkmode = store.getState().auth.darkmode;
  const [darkApp, setdarkApp] = useState(darkmode);

  const [Notifystate, dispatchState] = useReducer(
    notificationReducer,
    intitialNotificationState,
  );

  const [isNetWorkConnected, setIsNetWorkConnected] = useState(true);

  // memo
  const isNetWorkConnectedMemo = useMemo(
    () => isNetWorkConnected,
    [isNetWorkConnected],
  );

  const notiValue = useMemo(() => {
    return { Notifystate, dispatchState };
  }, [Notifystate, dispatchState]);

  const HomeStackNavigator = ({}: // navigation,
  // route,
  {
    navigation: any;
    route: any;
  }) => {
    return (
      <HomeStack.Navigator
        detachInactiveScreens={false}
        screenOptions={{ headerShown: false }}>
        {/* <HomeStack.Screen
          name="Home"
          component={Home}
          options={{ gestureEnabled: true }}
        /> */}
        <HomeStack.Screen
          name="SearchScreen"
          component={SearchScreen}
          options={{ gestureEnabled: true }}
        />
      </HomeStack.Navigator>
    );
  };
  const StoreStackNavigator = ({}: // navigation,
  // route,
  {
    navigation: any;
    route: any;
  }) => {
    return (
      <StoreStack.Navigator
        detachInactiveScreens={false}
        screenOptions={{ headerShown: false }}>
        <StoreStack.Screen
          name="MyHarbor"
          component={MyHarbor}
          options={{ gestureEnabled: true }}
        />
      </StoreStack.Navigator>
    );
  };
  const ProfileStackNavigator = ({}: // navigation,
  // route,
  {
    navigation: any;
    route: any;
  }) => {
    return (
      <ProfileStack.Navigator
        detachInactiveScreens={false}
        screenOptions={{ headerShown: false }}>
        <ProfileStack.Screen
          name="Pofile"
          component={ProfileScreen}
          // options={{ gestureEnabled: true }}
        />
        {/* <ProfileStack.Screen
          name="Settings"
          component={Settings}
          options={{gestureEnabled: true}}
        /> */}
      </ProfileStack.Navigator>
    );
  };
  const BottomTabsNavigator = () => {
    return (
      <Tab.Navigator
        initialRouteName={'HomeStackNavigator'}
        detachInactiveScreens={IOS ? true : false}
        tabBar={(props: any) =><BottomTabbar {...props} />}
        screenOptions={{
          headerShown: false,
          unmountOnBlur: true,
          tabBarHideOnKeyboard: true,
        }}>
        <Tab.Screen name="HomeStackNavigator" component={HomeStackNavigator} />
        <Tab.Screen
          name="ExploreStackNavigator"
          component={StoreStackNavigator}
        />
        <Tab.Screen
          name="StoreStackNavigator"
          component={JobPosting}
          listeners={({ navigation }) => ({
            tabPress: event => {
              event.preventDefault();
              navigation.navigate('JobPosting'); // Navigate to different Stack Navigator, which is created by createStackNavigator
            },
          })}
        />
        <Tab.Screen name="MessageNavigator" component={ChatScreen} />

        <Tab.Screen
          name="profilesetUpNavigator"
          component={ProfileStackNavigator}
        />
      </Tab.Navigator>
    );
  };
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener(
      'changeAppThemeMode',
      (data: any) => {
        setdarkApp(data);
      },
    );
    return () => {
      EventRegister.removeEventListener(eventListener);
    };
  }, []);

  useEffect(() => {
    NetInfo.addEventListener((state: {isConnected: any}) => {
      setIsNetWorkConnected(state.isConnected);
    });
  }, []);

  interface AppTheme extends Theme {
    dark: boolean;
  }
  function getTheme(): AppTheme {
    let themeObj: AppTheme = {
      ...DefaultTheme,
      colors: darkApp
        ? {
          ...DarkBaseColor,
        }
        : {
          ...BaseColors,
        },
    };

    return themeObj;
  }
  const linking = {
    enabled: true,
    prefixes: [
      'theharborapp://',
      'https://theharborapp.com',
    ],
    config: {
      screens: {
        ApplicantDetails: {
          path: 'user-detail/:userId',
          parse: {
            userId: (id: string | number) => `${id}`,
          },
        },
        JobApplicant: {
          path: 'job-details/:jobID',
          parse: {
            jobID: (id: string | number) => `${id}`,
          },
        },
        Tabs: {
          initialRouteName: 'BottomTabsNavigator',
          screens: {
            BottomTabsNavigator: {
              initialRouteName: 'HomeStackNavigator',
              screens: {
                HomeStackNavigator: {
                  path: 'event-management/:item',
                  parse: {
                    item: (value: string) => value,
                  },
                  stringify: {
                    item: (value: any) => `${value}`,
                  },
                },
              },
            },
          },
        },
      },
    },
  };

  const appTheme = getTheme();

  const forFade = ({ current }: StackCardInterpolationProps) => ({
    cardStyle: {
      opacity: current.progress,
    },
  });

  const handleDeepLink = useCallback(
    async (url: string) => {
      if (url) {
        const stripeHandled = await handleURLCallback(url);
        if (stripeHandled) {
          // This was a Stripe URL - you can return or add extra handling here as you see fit
        } else {
          // This was NOT a Stripe URL – handle as you normally would
        }
      }
    },
    [handleURLCallback],
  );

  useEffect(() => {
    const getUrlAsync = async () => {
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        handleDeepLink(initialUrl);
      }
    };

    getUrlAsync();

    const deepLinkListener = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    return () => deepLinkListener.remove();
  }, [handleDeepLink]);

  return (
    <SafeAreaProvider>
      {/* <StatusBar barStyle={'dark-content'} /> */}
      <Provider store={store}>
        <StripeProvider
          publishableKey={BaseSetting.stripeKey}
          // urlScheme="your-url-scheme" // required for 3D Secure and bank redirects
          merchantIdentifier="merchant.com.harbor.newapp" // required for Apple Pay
        >
          <NavigationContainer
            ref={navigationRef}
            theme={appTheme}
            linking={linking}>
            <NetworkLost isVisible={!isNetWorkConnectedMemo} />
            <NotificationContext.Provider value={notiValue}>
              {/* <RemotePushController /> */}
              <Stack.Navigator
                initialRouteName={'SplashScreen'}
                detachInactiveScreens={IOS ? true : false}
                screenOptions={{
                  headerShown: false,
                }}>
                <Stack.Screen
                  name="SplashScreen"
                  component={SplashScreen}
                  options={{
                    cardStyleInterpolator: forFade,
                    headerShown: false,
                    gestureEnabled: true,
                    animationEnabled: true,
                    cardOverlayEnabled: true,
                    cardStyle: { backgroundColor: 'transparent' },
                    detachPreviousScreen: false,
                  }}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="BottomTabsNavigator"
                  component={BottomTabsNavigator}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="IntroScreen"
                  component={IntroScreen}
                />
                <Stack.Screen
                  options={{ gestureEnabled: false }}
                  name="OTPVerify"
                  component={OTPVerify}
                />

                <Stack.Screen
                  options={{ gestureEnabled: false }}
                  name="AddName"
                  component={AdName}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="ProfileSetUp"
                  component={ProfileSetUp}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="PaymentScreen"
                  component={PaymentScreen}
                />
                <Stack.Screen
                  options={{ gestureEnabled: false }}
                  name="AuthScreen"
                  component={AuthScreen}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="Home"
                  component={Home}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="settings"
                  component={Settings}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="BankDetails"
                  component={BankDetailsForm}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="WelcomeScreen"
                  component={WelcomeScreen}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="JobPosting"
                  component={JobPosting}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="Applicants"
                  component={Applicants}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="FilterScreen"
                  component={FilterScreen}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="ReviewScreen"
                  component={ReviewScreen}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="ReviewList"
                  component={ReviewList}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="FeaturedList"
                  component={FeaturedList}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="GalleryView"
                  component={GalleryView}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="Wallet"
                  component={Wallet}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="KycCompleteScreen"
                  component={KycCompleteScreen}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="JobApplicant"
                  component={JobApplicant}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="JobDetailScreen"
                  component={JobDetailScreen}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="ApplicantDetails"
                  component={ApplicantDetails}
                />
                <Stack.Screen
                  options={{ gestureEnabled: true }}
                  name="Notification"
                  component={Notification}
                />
                <Stack.Screen
                  name="chat"
                  options={{ gestureEnabled: true }}
                  component={ChatScreen}
                />
                <Stack.Screen
                  name="Policy"
                  options={{ gestureEnabled: true }}
                  component={Policy}
                />
                <Stack.Screen
                  name="PrivacyPolicy"
                  options={{ gestureEnabled: true }}
                  component={PrivacyPolicy}
                />
                <Stack.Screen
                  name="WebViewScreen"
                  options={{ gestureEnabled: true }}
                  component={WebViewScreen}
                />
                <Stack.Screen
                  name="HarborHistory"
                  options={{ gestureEnabled: true }}
                  component={HarborHistory}
                />
                <Stack.Screen
                  name="AllHistory"
                  options={{ gestureEnabled: true }}
                  component={AllHistory}
                />
                <Stack.Screen
                  name="ChatDetails"
                  options={{ gestureEnabled: true }}
                  component={ChatDetails}
                />
                <Stack.Screen
                  name="AboutScreen"
                  options={{ gestureEnabled: true }}
                  component={AboutScreen}
                />
                <Stack.Screen
                  name="FAQs"
                  options={{ gestureEnabled: true }}
                  component={FAQs}
                />
                <Stack.Screen
                  name="ContactUs"
                  options={{ gestureEnabled: true }}
                  component={ContactUs}
                />
                <Stack.Screen
                  name="HarborPolicy"
                  options={{ gestureEnabled: true }}
                  component={HarborPolicy}
                />
                <Stack.Screen
                  name="CounterHistory"
                  options={{ gestureEnabled: true }}
                  component={CounterHistory}
                />
                <Stack.Screen
                  name="EditAccount"
                  options={{ gestureEnabled: true }}
                  component={EditAccount}
                />
                <Stack.Screen
                  name="SearchScreen"
                  options={{ gestureEnabled: false }}
                  component={SearchScreen}
                />
                <Stack.Screen
                  name="VerificationDetail"
                  options={{ gestureEnabled: true }}
                  component={VerificationDetail}
                />
                <Stack.Screen
                  name="VideoTutorial"
                  options={{ gestureEnabled: true }}
                  component={VideoTutorial}
                />
                <Stack.Screen
                  name="RewardScreen"
                  options={{ gestureEnabled: true }}
                  component={RewardScreen}
                />
                <Stack.Screen
                  name="RequestTag"
                  options={{ gestureEnabled: true }}
                  component={RequestTagScreen}
                />
                <Stack.Screen
                  name="SubmitForReview"
                  options={{ gestureEnabled: true }}
                  component={SubmitJobScreen}
                />
                <Stack.Screen
                  name="ViewTimeline"
                  options={{ gestureEnabled: true }}
                  component={ViewTimeline}
                />
              </Stack.Navigator>
              <BadgeWrapper />
              <SingInWrapper />
              <RemotePushNotification />
              <NewProfileCompleteModal />
            </NotificationContext.Provider>
          </NavigationContainer>
        </StripeProvider>
      </Provider>
    </SafeAreaProvider>
  );
}
