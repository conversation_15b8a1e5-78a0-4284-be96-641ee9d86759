/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useMemo, useState } from 'react';
import {
  Platform,
  View,
  Text,
  ScrollView,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import { translate } from '@language/Translate';
import { BaseColors } from '@config/theme';
import BaseSetting from '@config/setting';
import { FontFamily } from '@config/typography';
import { CustomIcon } from '@config/LoadIcons';
import { getCurrentLocation, isIOS } from '@app/utils/CommonFunction';

interface PlaceAutoCompleteProps {
  refs?: React.Ref<any>;
  isError?: boolean;
  isErrorMsg?: string;
  onAutoCompleteAddressSelect: (data: any, details: any | null) => void;
  placeholder?: string;
  onChangeText?: (text: string) => void;
  isDisable?: boolean;
  title?: string;
  mandatory?: boolean;
  location?: string;
  onFocus?: any;
  onBlur?: any;
  setLocation?: (location: any) => void;
}

const PlaceAutoComplete: React.FC<PlaceAutoCompleteProps> = ({
  refs,
  isError = false,
  isErrorMsg,
  onAutoCompleteAddressSelect,
  placeholder,
  onChangeText = () => {},
  isDisable = false,
  title,
  mandatory = false,
  location = '',
  onFocus = () => {},
  onBlur = () => {},
  setLocation = (location: any) => {},
}) => {
  const IOS = Platform.OS === 'ios';

  const [locLoder, setLocLoder] = useState(false);
  // To pre-populate location text in field
  useEffect(() => {
    if (location) {
      refs?.current?.setAddressText(location);
    }
  }, [location]);

  // State to control the dropdown visibility
  const [listViewDisplayed, setListViewDisplayed] = useState(false);
  const isListViewMemo = useMemo(() => listViewDisplayed, [listViewDisplayed]);
  const dropdownBorder = isListViewMemo ? 1 : 0;

  return (
    <ScrollView
      contentContainerStyle={{ zIndex: 10000, flexGrow: 1 }}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps={'handled'}>
      {/* Title with mandatory indicator */}
      {title ? (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={{
              color: BaseColors.inputColor,
              paddingBottom: 0,
              fontSize: 16,
              fontFamily: FontFamily.OpenSansRegular,
            }}>
            {title}
            {mandatory && (
              <Text style={{ fontSize: 15, color: BaseColors.inputColor }}>
                *
              </Text>
            )}
          </Text>
        </View>
      ) : null}

      {/* Google Places Autocomplete Component */}
      <GooglePlacesAutocomplete
        ref={refs}
        placeholder={placeholder || translate('addressPlaceholder', '')}
        query={{
          key: BaseSetting.googleApiKey,
          language: 'en',
        }}
        debounce={300}
        keepResultsAfterBlur={false}
        fetchDetails
        keyboardShouldPersistTaps="always"
        listViewDisplayed={false}
        textInputProps={{
          editable: !isDisable,
          placeholderTextColor: isError
            ? BaseColors.errorUpdatetxt
            : BaseColors.dividerColor,
          onChangeText: (txt: any) => {
            // onChangeText(txt);
            // setListViewDisplayed(false); // Show the dropdown on typing
          },
          onFocus: () => {
            onFocus();
          },
          onBlur: () => {
            onBlur();
          },
          // defaultValue: location,
          // value: location,
        }}
        onPress={(data, details = null) => {
          // setListViewDisplayed(false); // Close dropdown
          onAutoCompleteAddressSelect(data, details);
          setListViewDisplayed(false); // Close the dropdown on selection
          // refs?.current?.clear(); // Optionally clear the input
        }}
        styles={{
          container: {},
          listView: {
            backgroundColor: BaseColors.White,
            borderWidth: dropdownBorder,
            borderColor: '#E6EFFE',
            borderRadius: 10,
            paddingHorizontal: 10,
          },
          separator: {
            borderColor: '#E6EFFE',
            borderBottomWidth: 1,
          },
          row: {
            borderRadius: 7,
            backgroundColor: BaseColors.white,
          },
          poweredContainer: {
            display: 'none',
          },
          textInputContainer: {
            backgroundColor: BaseColors.white,
            borderRadius: 25,
            marginTop: 10,
            height: 50,
          },
          textInput: {
            backgroundColor: isError ? '#FFF2F1' : '#f5faff',
            fontSize: 14,
            color: isError ? BaseColors.errorUpdatetxt : BaseColors.primary,
            height: 50,
            borderWidth: 1,
            borderColor: 'transparent',
            borderRadius: 15,
            width: '90%',
            paddingHorizontal: 15,
            fontFamily: FontFamily.OpenSansRegular,
            paddingBottom: IOS ? 5 : 10,
            paddingRight: Dimensions.get('screen').width / 10,
          },
          description: {
            color: BaseColors.textGrey,
            fontFamily: FontFamily.OpenSansRegular,
          },
        }}
        renderRightButton={() => {
          return (
            <TouchableOpacity
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={async () => {
                setLocLoder(true);
                const locationData: any = await getCurrentLocation();

                setLocLoder(false);
                setLocation((p: any) => ({
                  ...p,
                  description: locationData?.description || '',
                  lat: locationData?.latitude || '',
                  long: locationData?.longitude || '',
                  shortAddress: locationData?.shortAddress || '',
                }));
              }}
              style={{
                position: 'absolute',
                right: 15,
                top: 15,
              }}>
              {locLoder ? (
                <ActivityIndicator color={BaseColors.primary} />
              ) : (
                <CustomIcon
                  name="Location"
                  size={20}
                  color={BaseColors.primary}
                />
              )}
            </TouchableOpacity>
          );
        }}
      />

      {/* Error Message */}
      {isError && (
        <Text
          style={{
            fontSize: 13,
            color: '#d62828',
            paddingBottom: 5,
            paddingTop: IOS ? 5 : 0,
            marginHorizontal: 4,
            fontFamily: FontFamily.OpenSansRegular,
          }}>
          {translate(isErrorMsg, '')}
        </Text>
      )}
    </ScrollView>
  );
};

export default React.memo(PlaceAutoComplete);

const styles = StyleSheet.create({
  container: {},
  listView: {
    backgroundColor: BaseColors.White,
    borderColor: '#E6EFFE',
    borderRadius: 10,
    paddingHorizontal: 10,
  },
  separator: {
    borderColor: '#E6EFFE',
    borderBottomWidth: 1,
  },
  row: {
    borderRadius: 7,
    backgroundColor: BaseColors.white,
  },
  poweredContainer: {
    display: 'none',
  },
  textInputContainer: {
    backgroundColor: BaseColors.white,
    borderRadius: 25,
    marginTop: 10,
    height: 50,
  },
  textInput: {
    backgroundColor: '#f5faff',
    fontSize: 14,
    color: BaseColors.primary,
    height: 50,
    borderWidth: 1,
    borderColor: 'transparent',
    borderRadius: 15,
    width: '90%',
    paddingHorizontal: 15,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: isIOS() ? 5 : 10,
    paddingRight: Dimensions.get('screen').width / 10,
  },
  description: {
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
  },
});
