import React from 'react';
import { Modal, View, Text, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAppDispatch, useAppSelector } from '@components/UseRedux';
import { translate } from '@language/Translate';
import { FontFamily } from '@config/typography';
import { BaseColors } from '@config/theme';
import authActions from '@redux/reducers/auth/actions';
import Button from '@components/UI/Button';

const { setCompleteProfilePopup } = authActions;

const NewProfileCompleteModal: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { showProfilePopup } = useAppSelector((state) => state.auth);

  const closeModal = () => {
    dispatch(setCompleteProfilePopup(false));
  };

  const goToProfile = () => {
    dispatch(setCompleteProfilePopup(false));
    navigation.navigate("ProfileSetUp");
    // navigation.navigate('ProfileSetUp' as never); // Change route name if different
  };

  return (
    <Modal
      visible={!!showProfilePopup}
      transparent
      animationType="fade"
      onRequestClose={closeModal}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <Text style={styles.title}>
            {translate('complete') || 'Complete Your Profile'}
          </Text>

          <Text style={styles.description}>
            {translate('createPostDesc') || 'Please complete your profile to continue.'}
          </Text>

          <View style={styles.buttonRow}>
            <Button containerStyle={{ minWidth: '48%' }} onPress={closeModal} type="outlined">
              <Text style={styles.cancelText}>
                {translate('createPostLater') || 'Later'}
              </Text>
            </Button>

            <Button containerStyle={{ minWidth: '48%' }} type="primary" onPress={goToProfile}>
              <Text style={styles.completeText}>
                {translate('createPostNow') || 'Complete Now'}
              </Text>
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 20,
  },
  modalContainer: {
    backgroundColor: BaseColors.white,
    borderRadius: 16,
    padding: 25,
    width: '100%',
    maxWidth: 400,
    elevation: 5,
  },
  title: {
    fontSize: 20,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.black,
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    fontSize: 15,
    fontFamily: FontFamily.OpenSansMedium,
    color: BaseColors.textGrey,
    textAlign: 'center',
    marginBottom: 40,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: BaseColors.lightGrey,
    alignItems: 'center',
  },
  completeButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: BaseColors.primary,
    alignItems: 'center',
  },
  cancelText: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 16,
  },
  completeText: {
    fontFamily: FontFamily.OpenSansMedium,
    fontSize: 16,
  },
});

export default NewProfileCompleteModal;
