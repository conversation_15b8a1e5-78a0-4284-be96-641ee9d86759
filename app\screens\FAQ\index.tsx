import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>ler,
  FlatList,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Toast from 'react-native-simple-toast';
import BaseSetting from '@config/setting';
import Header from '@components/Header';
import styles from './styles';
import { getApiData } from '@app/utils/apiHelper';
import { CustomIcon } from '@config/LoadIcons';
import { BaseColors } from '@config/theme';
import { ActivityIndicator } from 'react-native';
import NoRecord from '@components/NoRecord';
import TextInput from '@components/UI/TextInput';
import { translate } from '@language/Translate';
import WebView from 'react-native-webview';
import AnimatedView from '@components/AnimatedView';

export default function FAQs({ navigation }: any) {
  const [faqList, setFaqList] = useState<any[]>([]);
  const [clickId, setClickId] = useState<number | null>(null);
  const [search, setSearch] = useState('');
  const [loader, setLoader] = useState(false);
  const [webViewHeights, setWebViewHeights] = useState<{[key: number]: number}>({}); // Store heights by item ID


  const getFaqData = async (query = '') => {
    setLoader(true);
    const data: any = {};
    if (query.trim().length > 0) {
      data.search = query;
    }

    const url = BaseSetting.endpoints.faqList;
    try {
      const resp = await getApiData({ endpoint: url, method: 'GET', data });
      if (resp?.status) {
        setFaqList(resp?.data || []); // Set empty array if data is null/undefined
      } else {
        setFaqList([]);
        Toast.show(resp?.message || 'Failed to fetch FAQs');
      }
    } catch (error) {
      setFaqList([]);
      // Toast.show('Something went wrong');
    } finally {
      setLoader(false);
    }
  };

  const handleSearch = (text: any) => {
    setSearch(text);
    getFaqData(text);
  };

  const handleBackButtonClick = () => {
    navigation.goBack();
    return true;
  };

  useEffect(() => {
    getFaqData();
  }, []);

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const renderItem = ({ item }: any) => {
    const isExpanded = clickId === item.id;

    return (
      <View style={styles.mainView}>
        <TouchableOpacity
          style={styles.titleView}
          onPress={() => setClickId(isExpanded ? null : item.id)}>
          <Text style={styles.questionText}>
            {item?.question || 'No question available'}
          </Text>
          <CustomIcon
            name={'ArrowUp'}
            size={24}
            color={BaseColors.inputColor}
            style={{ transform: [{ rotate: isExpanded ? '360deg' : '180deg' }] }}
          />
        </TouchableOpacity>
        {isExpanded && (
          <View style={styles.answerView}>
            {item?.answer ? (
              <WebView
                originWhitelist={['*']}
                source={{
                  html: `
                    <html>
                      <head>
                        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
                        <style>
                          * {
                            box-sizing: border-box;
                          }
                          html, body {
                            margin: 0;
                            padding: 0;
                            height: auto;
                            overflow: hidden;
                          }
                          body {
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
                            color: #333;
                            font-size: 16px;
                            line-height: 1.4;
                            padding: 0;
                            margin: 0;
                          }
                          p {
                            margin: 0 0 4px 0;
                          }
                          p:last-child {
                            margin-bottom: 0;
                          }
                          img {
                            max-width: 100%;
                            height: auto;
                          }
                          a {
                            color: #0066cc;
                            text-decoration: none;
                          }
                          .content-wrapper {
                            padding: 0;
                            margin: 0;
                            display: inline-block;
                            width: 100%;
                          }
                        </style>
                      </head>
                      <body>
                        <div class="content-wrapper">${item?.answer}</div>
                      </body>
                    </html>
                  `,
                }}
                style={{
                  height: webViewHeights[item.id] || 20, // Minimal default height
                  width: '100%',
                  opacity: webViewHeights[item.id] ? 1 : 0, // Hide until height is calculated
                }}
                scrollEnabled={false}
                scalesPageToFit={false}
                injectedJavaScript={`
                  (function() {
                    // More accurate height calculation function
                    function calculateHeight() {
                      // Get the content wrapper element
                      const content = document.querySelector('.content-wrapper');

                      // Calculate the exact height of the content
                      const height = content ? content.offsetHeight : document.body.offsetHeight;

                      // Send the height back to React Native
                      window.ReactNativeWebView.postMessage(JSON.stringify({height: height}));
                    }

                    // Calculate height after initial load
                    setTimeout(calculateHeight, 50);

                    // Add a mutation observer to detect content changes
                    const observer = new MutationObserver(calculateHeight);

                    // Observe the entire document for changes
                    observer.observe(document.body, {
                      subtree: true,
                      childList: true,
                      attributes: true,
                      characterData: true
                    });

                    // Also recalculate on window resize
                    window.addEventListener('resize', calculateHeight);
                  })();
                  true;
                `}
                onMessage={(event) => {
                  try {
                    const { height } = JSON.parse(event.nativeEvent.data);
                    // Set the exact height with minimal buffer
                    const newHeight = Math.max(height, 20); // Ensure minimum height of 20px

                    // Only update if height has changed significantly (more than 1px)
                    if (Math.abs((webViewHeights[item.id] || 0) - newHeight) > 1) {
                      setWebViewHeights(prev => ({
                        ...prev,
                        [item.id]: newHeight,
                      }));
                    }
                  } catch (error) {
                    console.log('Error parsing WebView height:', error);
                  }
                }}
              />
            ) : (
              <Text style={styles.answerText}>No answer available</Text>
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles?.container}>
      <Header
        title="FAQs"
        leftIcon="back-arrow"
        onLeftPress={() => navigation.goBack()}
      />
      <AnimatedView>
        <KeyboardAwareScrollView
          bounces={false}
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          enableOnAndroid={false}>
          <View style={styles.mainContainer}>
            <View style={styles?.searchView}>
              <TextInput
                value={search}
                onChange={(txt: any) => handleSearch(txt)}
                searchButton={true}
                placeholderText={translate('Search', '')}
              />
            </View>
            {loader ? (
              <View
                style={{ alignSelf: 'center', flex: 1, justifyContent: 'center' }}>
                <ActivityIndicator size="small" color={BaseColors.primary} />
              </View>
            ) : (
              <FlatList
                data={faqList}
                renderItem={renderItem}
                keyExtractor={(item, index) =>
                  item?.id?.toString() || index.toString()
                }
                ListEmptyComponent={
                  <View style={styles.centerMain}>
                    <NoRecord
                      title={'No FAQ'}
                      type="employer"
                      iconName="employer"
                    />
                  </View>
                }
              />
            )}
          </View>
        </KeyboardAwareScrollView>
      </AnimatedView>
    </View>
  );
}
