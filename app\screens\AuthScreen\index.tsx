import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  BackHandler,
  Dimensions,
  Keyboard,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { AppleButton } from '@invertase/react-native-apple-authentication';
// import Toast from 'react-native-toast-message';
import Toast from 'react-native-simple-toast';
import { translate } from '@language/Translate';
import { useDispatch, useSelector } from 'react-redux';
import authActions from '@redux/reducers/auth/actions';
import auth from '@react-native-firebase/auth';
import styles from './styles';
// import * as Sentry from '@sentry/react-native';
import { isValidPhoneNumber } from 'libphonenumber-js';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting, { updateBaseUrl } from '@config/setting';
import {
  signInWithGoogle,
  signOutGoogle,
  signinWithApple,
} from '@app/utils/socialLogin';
import { BaseColors } from '@config/theme';
import { Images } from '@config/images';
import Button from '@components/UI/Button';
import TextInput from '@components/UI/TextInput';
import { isEmpty } from '@app/utils/lodashFactions';
import FastImage from 'react-native-fast-image';
import AuthAuthentication from '@redux/reducers/auth/actions';
import { getFBErrorMessage, isIOS, updateReview } from '@app/utils/CommonFunction';
import { getToken } from '@components/Common/PushNotification';
import socketAction from '@redux/reducers/socket/actions';
import NavigationService from '@navigation/NavigationService';




const { width } = Dimensions.get('window');

const AppleButtonWidth = Dimensions.get('screen').width / 1.155;
interface Props {
  route: any;
}
// interface CheckUserResult {
//   isUserExists: any; // Replace `any` with the specific type if known
//   message: string;
//   data?: any;
//   status: boolean;
//   success?: boolean;
// }

interface ApiDataResponse {
  isUserExists: any; // Replace `any` with the actual type
  data: any;
  status: boolean;
}

interface ErrorResponse {
  status: false;
}

interface PhoneData {
  countryCode: string;
  phoneNumber: string;
  otp_send?: boolean;
}

interface GoogleData {
  googleId: string;
}

interface AppleData {
  appleId: string;
}

type Data = PhoneData | GoogleData | AppleData;
type CheckUserReturn = ApiDataResponse | ErrorResponse;

export default function AuthScreen({ route }: Props) {
  let backPressed = 0;
  const { params } = route;
  const type = params?.type;
  const onClose = params?.onClose;
  const { userProfileData, confirmationResult } = useSelector((state: any) => state.auth); // Use your RootState type
  const { setUserProfileData } = AuthAuthentication;

  const [authType, setType] = useState<'login' | 'signup'>(type || 'login');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [flagCode, setFlagCode] = useState<string>('Us'); // Initial country code as a string
  const [phoneNumberError, setPhoneNumberError] = useState<boolean>(false);
  const [phoneNumberErrorTxt, setPhoneNumberErrorTxt] = useState<string>('');
  const [loader, setLoader] = useState<any>(false);
  const [state, setState ] = useState({
    otpScreenVisible: false,
    nameScreenVisible: false,
  });
  const dispatch = useDispatch();
  const [countryCode, setCountryCode] = useState<string>('1'); // Initial country code as a string
  const { fcmToken } = useSelector((s: any) => s.notification);

  // State for the hidden feature
  const [pressCount, setPressCount] = useState(0);
  const [currentEnv, setCurrentEnv] = useState('beta'); // 'beta' or 'dev'

  const { otpScreenVisible, nameScreenVisible } = state;


  const isLogin = authType === 'login';

  const validatePhoneNumber = (number: string, code: string) => {
    const fullPhoneNumber = `+${code}${number}`;
    return isValidPhoneNumber(fullPhoneNumber);
  };

  useEffect(() => {
    if (!fcmToken) {
      getToken();
    }
  }, [fcmToken]);


  const getOtp = async (
    code: number,
    phoneNumber: string,
    type: string,
    isCheckUser: any,
    isTwilioApiEnabled: boolean,
  ) => {
    try {
      const confirmationResult = await auth().signInWithPhoneNumber(
        `+${code}${phoneNumber}`,
      );
      dispatch(authActions.setConfirmationResult(confirmationResult) as any);
      onClose && onClose();
      NavigationService.navigate('OTPVerify', {
        confirmationResult,
        code,
        flagCode,
        phoneNumber,
        type,
        isUserExists: isCheckUser?.data?.isUserExists,
        isTwilioApiEnabled,
      });
      setLoader(false);
      // setConfirmation(confirmationResult);
    } catch (error) {
      const errorMessage = getFBErrorMessage(error);
      Toast.show(
        errorMessage,
        Toast.LONG,
      );
      setLoader(false);
    }
  };

  const checkUser = async (
    code: string,
    phoneNumber: string,
    type: string,
  ): Promise<CheckUserReturn> => {
    try {
      let data: Data | undefined;

      if (type === 'phone') {
        data = {
          countryCode: code,
          phoneNumber: phoneNumber,
          otp_send: true,
        };
      }
      if (type === 'google') {
        data = {
          googleId: code,
        };
      }

      if (type === 'apple') {
        data = {
          appleId: code,
        };
      }
      const confirmationResult = await getApiData({
        endpoint: BaseSetting.endpoints.getUser,
        method: 'POST',
        data,
      });
      if (confirmationResult?.status) {
        return { ...confirmationResult, success: true };
      } else {
        return { ...confirmationResult, status: false };
      }
    } catch (error) {
      console.log(
        '🚀 ~ validateAndProceed ~ error:',
        error,
        `+${code}${phoneNumber}`,
      );
      return { status: false };
    }
  };
  const userLoginSingup = async (profileDetails: any, type: string) => {
    try {
      let data;
      const fToken = fcmToken ? fcmToken : await getToken();
      console.log('fToken ===>', fcmToken || 'no token', await getToken());

      if (type === 'google') {
        data = {
          idToken: profileDetails?.data?.idToken,
          type,
          fcmToken: fToken,
          fcmPlatform: isIOS() ? 'ios' : 'android',
        };
      }

      if (type === 'apple') {
        data = {
          idToken: profileDetails?.apple_data?.identityToken,
          // JSON.stringify(profileDetails),
          type,
          fcmToken: fToken,
          fcmPlatform: isIOS() ? 'ios' : 'android',
        };
      }
      const response = await getApiData({
        endpoint: BaseSetting.endpoints.socialAuth,
        method: 'POST',
        data: data,
      });
      console.log('🚀 ~ userLoginSingup ~ response:', response);
      if (response?.status) {
        const isNewUser = response?.data?.isNewUser || false;
        onClose && onClose();
        if (isNewUser) {
          const pData =
            type === 'apple' ? profileDetails?.apple_data?.fullName : {};
          dispatch(
            setUserProfileData({
              ...userProfileData,
              firstName:
                response?.data?.user?.firstName ||
                pData?.givenName ||
                pData?.middleName,
              lastName: response?.data?.user?.lastName || pData?.familyName,
              email:
                type === 'apple'
                  ? response?.data?.user?.appleId
                  : response?.data?.user?.googleId,
            }) as any,
          );
          dispatch(authActions.setUserData({ ...response?.data?.user }) as any);
          dispatch(authActions.setAccessToken(response?.data?.token) as any);
          NavigationService.replace('AddName');
        } else {
          updateReview('daily_login', response?.data?.user?.id);
          dispatch(
            setUserProfileData({
              ...userProfileData,
              firstName: response?.data?.user?.firstName,
              lastName: response?.data?.user?.lastName,
            }) as any,
          );
          dispatch(authActions.setUserData(response?.data?.user) as any);
          dispatch(authActions.setAccessToken(response?.data?.token) as any);
          NavigationService.replace('BottomTabsNavigator');
        }
      } else {
        Toast.show(
          response?.data?.message ||
            response?.error ||
            'Something went wrong while verification, please try again later!',
          Toast.LONG,
        );
      }
      setLoader(false);
    } catch (e) {
      // Toast.show(
      //   'Something went wrong while verification, please try again later!',
      //   Toast.BOTTOM,
      // );
      setLoader(false);
      return { status: false };
    }
  };

  const userDirection = (profileDetails: any, type: string) => {
    setLoader(type);
    if (!isEmpty(profileDetails)) {
      userLoginSingup(profileDetails, type);
    } else {
      setLoader(false);
      // Toast.show('Something went wrong please try again later!', Toast.SHORT);
    }
  };

  const validateAndProceed = async (type?: string) => {
    const code =
      typeof countryCode === 'object'
        ? countryCode.callingCode[0]
        : countryCode;
    if (phoneNumber === '') {
      setPhoneNumberError(true);
      setPhoneNumberErrorTxt('Please enter phone number');
    } else if (!validatePhoneNumber(phoneNumber, code)) {
      setPhoneNumberError(true);
      setPhoneNumberErrorTxt('Please enter a valid phone number');
    } else {
      setPhoneNumberError(false);
      setPhoneNumberErrorTxt('');
      const type = 'phone';
      setLoader(true);
      const resp = await checkUser(
        `+${code}`,
        phoneNumber,
        type,
      );
      console.log('🚀 ~ validateAndProceed ~ resp:', resp);
      if (resp?.status === true) {
        const isTwilioApiEnabled = isIOS() ? resp?.data?.TwForIOS : resp?.data?.TwForAndroid;
        if (!resp?.data?.isUserExists && authType === 'login') {
          Toast.show(resp?.message || translate('userNotFound', ''), Toast.LONG);
          // Toast.show({
          //   type: 'error',
          //   text1: 'Authentication Error',
          //   text2: resp?.message || translate('userNotFound', ''),
          //   visibilityTime: 5000,
          // });
          setLoader(false);
        } else {
          // If Twilio enabled → current flow
          if (isTwilioApiEnabled) {
            onClose && onClose();
            NavigationService.navigate('OTPVerify', {
              confirmationResult,
              code,
              flagCode,
              phoneNumber,
              type,
              isUserExists: resp?.data?.isUserExists,
              isTwilioApiEnabled,
            });
          } else {
            // If Twilio disabled → use Firebase OTP flow
            await getOtp(code, phoneNumber, type, resp, isTwilioApiEnabled);
          }
        }
      } else {
        setLoader(false);
        Toast.show(resp?.data?.message || translate('err', ''), Toast.SHORT);
      }
    }
    // Sentry.captureException(new Error('Test Error'));
  };

  const toggleAuthMode = () => {
    setType(prevType => (prevType === 'login' ? 'signup' : 'login'));
    // setPhoneNumber('');
    // setPhoneNumberErrorTxt('');
    // setPhoneNumberError(false);
  };

  // this is for hard back from app....
  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      // Toast.show("Press again to exit");
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }
  // Function to handle the logo press for environment switching
  const handleLogoPress = async () => {
    const newCount = pressCount + 1;
    setPressCount(newCount);

    // If pressed 5 times, toggle the environment
    if (newCount >= 5) {
      const newEnv = currentEnv === 'beta' ? 'dev' : 'beta';
      const newBaseUrl = currentEnv === 'beta'
        ? 'https://api.theharborapp.com'
        : 'https://beta.theharborapp.com';

      // Update the baseUrl (now async)
      await updateBaseUrl(newBaseUrl);

      // Reset press count
      setPressCount(0);

      // Update current environment
      setCurrentEnv(newEnv);

      // Reinitialize socket connection with new baseUrl
      try {
        dispatch(socketAction.initialization());
      } catch (error) {
        console.log('Error reinitializing socket:', error);
      }

      // Show alert to confirm the change
      Alert.alert(
        'Environment Changed',
        `Base URL has been updated to: ${newBaseUrl}`,
        [{ text: 'OK' }]
      );
    }
  };

  useEffect(() => {
    // Initialize currentEnv based on the current baseUrl
    if (BaseSetting.baseUrl === 'https://beta.theharborapp.com') {
      setCurrentEnv('beta');
    } else {
      setCurrentEnv('dev');
    }

    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        bounces={false}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={false}>
        <View style={styles.innerContainer}>
          <View style={styles.mainImgView}>
            <TouchableOpacity
              style={styles.imgView}
              activeOpacity={1}
              onPress={handleLogoPress}
            >
              <FastImage
                source={Images.harborLogo}
                resizeMode={FastImage.resizeMode.contain} // Ensure proper scaling
                style={{ width: '100%', height: '100%' }} // Match the image's natural dimensions
              />
              {/* Small indicator dot when in dev mode
                {currentEnv === 'dev' && (
                  <View style={styles.devIndicator} />
                )} */}
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer}>
            <TextInput
              phoneNumber
              value={phoneNumber}
              title={
                isLogin ? translate('signIn', '') : translate('signUpdata', '')
              }
              placeholderText={translate('mobileNumber', '')}
              showError={phoneNumberError}
              errorText={phoneNumberErrorTxt}
              // maxLength={10}
              onSubmit={() => {
                validateAndProceed();
                Keyboard.dismiss();
              }}
              onChange={(txt: string) => {
                const cleanedTxt = txt.trim().replace(/[^\d]/g, '');
                setPhoneNumber(cleanedTxt);
                if (validatePhoneNumber(cleanedTxt, countryCode)) {
                  setPhoneNumberError(false);
                  setPhoneNumberErrorTxt('');
                }
              }}
              onCountryChange={(codeObj: any) => {
                const code = codeObj.callingCode[0];
                setCountryCode(code);
                setFlagCode(codeObj?.cca2);
                if (validatePhoneNumber(phoneNumber, code)) {
                  setPhoneNumberError(false);
                  setPhoneNumberErrorTxt('');
                } else {
                  if (isEmpty(phoneNumber)) {
                    setPhoneNumberError(false);
                    setPhoneNumberErrorTxt('');
                  } else {
                    setPhoneNumberError(true);
                    setPhoneNumberErrorTxt('Please enter a valid phone number');
                  }
                }
              }}
              containerSty={!phoneNumberError && styles.noErrorContainer}
              titleSty={styles.inputTitle}
              textInputStyle={styles.textInput}
              codeTxtSty={styles.codeText}
              keyBoardType="number-pad"
              returnKeyType="done"
              style={{
                borderColor: phoneNumberError
                  ? BaseColors.red
                  : BaseColors.primary,
                borderWidth: width * 0.003,
                borderRadius: width * 0.02,
              }}
              mandatory={false}
              maxLength={20}
            />
            {isLogin ? null : (
              <View>
                <Text style={styles.verifyCodeText}>
                  {translate('verifyCode', '')}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.buttonContainer}>
            <Button
              onPress={validateAndProceed}
              containerStyle={styles.buttonStyle}
              loading={loader === true}
              type="text">
              {translate('Continue', '')}
            </Button>
          </View>

          <View style={styles.toggleContainer}>
            <Text style={styles.toggleText}>
              {translate(isLogin ? 'needAccount' : 'allreadyaccount', '')}
            </Text>
            <TouchableOpacity onPress={toggleAuthMode} activeOpacity={0.8}>
              <Text style={styles.toggleActionText}>
                {isLogin
                  ? translate('signUpdata', '')
                  : translate('signIn', '')}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={styles.dividerContainer}>
            <View style={styles.divider} />
            <Text style={styles.orText}>{translate('Or', '')}</Text>
            <View style={styles.divider} />
          </View>

          {isIOS() ? (
            <>
              <View style={{ alignItems: 'center' }}>
                {loader === 'apple' ? (
                  <Button
                    type="outlined"
                    loading={loader === 'apple'}
                    style={{
                      width: AppleButtonWidth,
                      borderColor: BaseColors.textGrey,
                      color: BaseColors.textGrey,
                    }}
                  />
                ) : (
                  <AppleButton
                    buttonStyle={AppleButton.Style.WHITE_OUTLINE}
                    buttonType={AppleButton.Type.CONTINUE}
                    style={{
                      width: AppleButtonWidth,
                      height: 45, // You must specify a height
                    }}
                    textStyle={styles.continueText}
                    onPress={async () => {
                      const resp = await signinWithApple();
                      console.log('🚀 ~ onPress={ ~ resp:', resp);
                      userDirection(resp, 'apple');
                    }}
                  />
                )}
              </View>
              <View style={{ paddingVertical: 15 }}>
                <Button
                  googleIcon
                  type="outlined"
                  loading={loader === 'google'}
                  onPress={async () => {
                    const profileDetails = await signInWithGoogle();
                    signOutGoogle();
                    if (!isEmpty(profileDetails?.data)) {
                      userDirection(profileDetails, 'google');
                    }
                  }}
                  style={{ ...styles.googleButton, width: AppleButtonWidth }}>
                  <Text style={styles.continueText}>
                    {translate('continueGoggle', '')}
                  </Text>
                </Button>
              </View>
            </>
          ) : (
            <Button
              googleIcon
              type="outlined"
              onPress={async () => {
                const profileDetails = await signInWithGoogle();
                signOutGoogle();
                if (!isEmpty(profileDetails?.data)) {
                  userDirection(profileDetails, 'google');
                }
              }}
              loading={loader === 'google'}
              style={styles.googleButton}>
              <Text style={styles.continueText}>
                {translate('continueGoggle', '')}
              </Text>
            </Button>
          )}
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
}
