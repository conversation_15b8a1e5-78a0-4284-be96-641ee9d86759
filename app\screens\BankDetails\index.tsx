import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import InsetWrapper from '@components/InsetWrapper';
import Header from '@components/Header';
import CInput from '@components/UI/TextInput';
import CButton from '@components/UI/Button';
import { translate } from '@language/Translate';
import SSNInput from '@components/UI/TextInput/SSNInput';
import DropdownList from '@components/DropDownList';
import Toast from 'react-native-simple-toast';
import { states } from '@config/staticdata';
import { useAppSelector } from '@components/UseRedux';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import styles from './styles';
import AnimatedView from '@components/AnimatedView';
import { BaseColors } from '@config/theme';
import { isIOS, updateUserData } from '@app/utils/CommonFunction';
import BankAccountCard from '@components/BankAccountDetails';

interface FormData {
  firstName: string;
  lastName: string;
  routingNumber: string;
  phone: string | undefined;
  accountNumber?: string;
  dob: string | undefined | null;
  ssnLast4: string;
  street1: string;
  street2: string;
  city: string;
  state: string;
  zip: string;
}

const formatDOB = (dob: { day: string; month: string; year: string }) => {
  if (!dob?.day || !dob?.month || !dob?.year) {return '';}
  return `${dob.year}-${dob.month.padStart(2, '0')}-${dob.day.padStart(2, '0')}`;
};

const isPastDate = (value: string) => {
  const today = new Date();
  const input = new Date(value);
  return input < today || translate('dateOfBirthMustBeInThePast');
};

const BankDetailsForm = ({ navigation }: any) => {
  const { userData } = useAppSelector((s) => s.auth);

  const [loader, setLoader] = useState(false);
  const [fetchLoader, setFetchLoader] = useState(false);
  const [maskedAccountDisplay, setMaskedAccountDisplay] = useState('');
  const [isBankAccountEdit, setIsBankAccountEdit] = useState(false);
  const [state, setState] = useState({
    bankData: {},
  });
  const { bankData } = state;
  const [countryCode, setCountryCode] = useState<string>('1'); // Initial country code as a string

  // For modal visible (Bank setup)
  // const [modalVisible, setModalVisible] = useState(true);

  const fNameRef = useRef<any>(null);
  const lNameRef = useRef<any>(null);
  const phoneRef = useRef<any>(null);
  const accountRef = useRef<any>(null);
  const routingRef = useRef<any>(null);
  const ssnRef = useRef<any>(null);
  const street1Ref = useRef<any>(null);
  const street2Ref = useRef<any>(null);
  const cityRef = useRef<any>(null);
  const zipRef = useRef<any>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<FormData>({
    defaultValues: {
      firstName: undefined,
      lastName: undefined,
      routingNumber: '',
      phone: '',
      accountNumber: undefined,
      dob: undefined,
      ssnLast4: undefined,
      street1: undefined,
      street2: undefined,
      city: undefined,
      state: undefined,
      zip: undefined,
    },
  });

  const onSubmit = async (form: FormData) => {
    setLoader(true);
    try {
      console.log('form ===>', form?.dob);
      const dob = new Date(form.dob); // input is ISO format
      const payload = {
        firstName: form.firstName.trim(),
        lastName: form.lastName.trim(),
        phone: `+${countryCode}${form.phone}`,
        dob: {
          day: String(dob.getUTCDate()).padStart(2, '0'),
          month: String(dob.getUTCMonth() + 1).padStart(2, '0'),
          year: String(dob.getUTCFullYear()) },
        ssnLast4: form.ssnLast4,
        routingNumber: form.routingNumber,
        accountNumber: form.accountNumber,
        address: {
          line1: form.street1.trim(),
          line2: form.street2?.trim() || '',
          city: form.city.trim(),
          state: form.state,
          postal_code: form.zip,
        },
      };
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateBank,
        method: 'POST',
        data: payload,
      });
      if (res?.status === true) {
        updateUserData(userData?.id);
        setValue('accountNumber', undefined);
        setValue('ssnLast4', undefined);
        const accountNumber = form.accountNumber || '';
        const maskedAccount = accountNumber
          ? `${'*'.repeat(accountNumber.length - 4)}${accountNumber.slice(-4)}`
          : '';
        setMaskedAccountDisplay(maskedAccount);
        setIsBankAccountEdit(false);
        Toast.show(res?.message || translate('bankUpdatedSuccessfully'), Toast.BOTTOM);
        // setModalVisible(true);
      } else {
        Toast.show(res?.message || translate('err'), Toast.BOTTOM);
      }
      console.log('res ==>', res);
      setLoader(false);
    } catch (err) {
      console.log('res ==>', err);
      setLoader(false);
      Toast.show(translate('err'), Toast.BOTTOM);
    }
  };

  const getBankDetails = async () => {
    setFetchLoader(true);
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.getBankDetails,
        method: 'GET',
      });
      console.log('res ===>', res);
      if (res?.status === true) {
        const apiData = res.data;
        console.log('apiData ===>', apiData);
        const accountNumber = String(apiData?.accountNumber || '');
        const maskedAccount = accountNumber !== ''
          ? `${'*'.repeat(accountNumber?.length - 4)}${accountNumber?.slice(-4)}`
          : '';
        const phoneNo = String(apiData?.phone || '');
        const address = apiData?.address || {};
        setMaskedAccountDisplay(maskedAccount);
        const formatted: FormData = {
          firstName: apiData?.firstName || undefined,
          lastName: apiData?.lastName || undefined,
          routingNumber: apiData?.routingNumber || undefined,
          phone: phoneNo?.startsWith('+1') ? phoneNo.slice(2) : phoneNo || undefined,
          // accountNumber: apiData.accountNumber || undefined,
          dob: apiData?.dob?.day !== '' ? formatDOB(apiData.dob) : undefined,
          ssnLast4: apiData?.ssnLast4 || undefined,
          street1: address?.line1 || undefined,
          street2: address?.line2 || undefined,
          city: address?.city || undefined,
          state: address?.state || undefined,
          zip: address?.postal_code || undefined,
        };
        setIsBankAccountEdit(apiData?.routingNumber && apiData?.accountNumber ? false : true);
        setState((p) => ({ ...p, bankData: apiData }));
        reset(formatted);
      } else {
        setIsBankAccountEdit(true);
        Toast.show(res?.data?.message || res?.message || translate('err'), Toast.BOTTOM);
      }
      setLoader(false);
      setFetchLoader(false);
    } catch (err) {
      setIsBankAccountEdit(true);
      setFetchLoader(false);
      setLoader(false);
      Toast.show(err?.message || translate('err'), Toast.BOTTOM);
    }
  };

  useEffect(() => {
    getBankDetails();
    return () => {};
  }, []);

  return (
    <InsetWrapper>
      <View style={styles.flex}>
        <Header
          title={translate('bankAccount')}
          leftIcon="back-arrow"
          onLeftPress={() => navigation.goBack()}
        />
        {!fetchLoader && isBankAccountEdit && (
          <Text style={styles.mainLabel}>
            {translate('updateYourBankAccountToChangeHowYouGetPaid')}
          </Text>)}
        {fetchLoader && (
          <View style={styles.loaderContainer}>
            <ActivityIndicator color={BaseColors.primary} size={'small'} />
          </View>
        )}
        {!fetchLoader && !isBankAccountEdit ? (
          <AnimatedView>
            <BankAccountCard
              companyName={bankData?.bankName || '-'}
              accountType={translate('accountNumber')}
              last4={maskedAccountDisplay}
              onUpdate={() => {setIsBankAccountEdit(true);}}
              status={userData?.bankAccountVerified}
            />
          </AnimatedView>
        ) : !fetchLoader &&  (
          <>
            <KeyboardAvoidingView
              behavior={isIOS() ? 'padding' : 'height'}
              style={{ flex: 1 }}
              enabled
              keyboardVerticalOffset={isIOS() ? 0 : 70}
            >
              <ScrollView
                contentContainerStyle={styles.scrollContainer}
                keyboardShouldPersistTaps="handled"
              >
                <AnimatedView>
                  <Controller
                    name="firstName"
                    control={control}
                    rules={{
                      required: translate('firstNameIsRequired'),
                      minLength: { value: 2, message: translate('atLeast2Characters') },
                    }}
                    render={({ field }) => (
                      <CInput
                        title={translate('firstName')}
                        placeholderText={translate('enterFirstName')}
                        {...field}
                        returnKeyType="next"
                        ref={fNameRef}
                        onSubmit={() => {
                          lNameRef?.current?.focus();
                        }}
                        mandatory
                        showError={!!errors.firstName}
                        errorText={errors.firstName?.message}
                      />
                    )}
                  />

                  <Controller
                    name="lastName"
                    control={control}
                    rules={{
                      required: translate('lastNameIsRequired'),
                      minLength: { value: 2, message: translate('atLeast2Characters') },
                    }}
                    render={({ field }) => (
                      <CInput
                        title={translate('lastName')}
                        placeholderText={translate('enterLastName')}
                        {...field}
                        returnKeyType="next"
                        ref={lNameRef}
                        onSubmit={() => {
                          routingRef?.current?.focus();
                        }}
                        mandatory
                        showError={!!errors.lastName}
                        errorText={errors.lastName?.message}
                      />
                    )}
                  />

                  <Controller
                    name="routingNumber"
                    control={control}
                    rules={{
                      required: translate('routingNumberIsRequired'),
                      pattern: {
                        value: /^\d{9}$/,
                        message: translate('routingNumberMustBeExactly9Digits'),
                      },
                    }}
                    render={({ field }) => (
                      <CInput
                        title={translate('routingNumber')}
                        placeholderText={translate('routingNumberEx123456789')}
                        keyboardType="number-pad"
                        {...field}
                        returnKeyType="next"
                        ref={routingRef}
                        onSubmit={() => {
                          accountRef?.current?.focus();
                        }}
                        mandatory
                        showError={!!errors.routingNumber}
                        errorText={errors.routingNumber?.message}
                      />
                    )}
                  />

                  <Controller
                    name="accountNumber"
                    control={control}
                    rules={{
                      required: translate('accountNumberIsRequired'),
                      pattern: {
                        value: /^\d{6,17}$/,
                        message: translate('accountNumberMustBe617Digits'),
                      },
                    }}
                    render={({ field }) => (
                      <CInput
                        title={`${translate('accountNumber')}${maskedAccountDisplay ? ` (${maskedAccountDisplay})` : ''}`}
                        placeholderText={translate('enterAccountNumber')}
                        keyBoardType="number-pad"
                        {...field}
                        returnKeyType="next"
                        ref={accountRef}
                        onSubmit={() => {
                          phoneRef?.current?.focus();
                        }}
                        mandatory
                        showError={!!errors.accountNumber}
                        errorText={errors.accountNumber?.message}
                      />
                    )}
                  />

                  <Controller
                    name="phone"
                    control={control}
                    rules={{
                      required: translate('phoneNumberIsRequired'),
                      pattern: {
                        value: /^\d{10}$/,
                        message: translate('phoneNumberMustBe10Digits'),
                      },
                    }}
                    render={({ field }) => (
                      <CInput
                        title={translate('phoneNumber')}
                        phone
                        placeholderText={translate('enterPhoneNumber')}
                        keyBoardType="number-pad"
                        phoneNumber
                        {...field}
                        returnKeyType="next"
                        ref={phoneRef}
                        onSubmit={() => {
                          ssnRef?.current?.focus();
                        }}
                        mandatory
                        countryCodeEditable={false}
                        callingCode={countryCode}
                        // countryCode={
                        //   flagCode
                        //     ? String(flagCode)?.toUpperCase()
                        //     : gettingFlagCode
                        // }
                        onCountryChange={(codeObj: any) => {
                          const code = codeObj.callingCode[0];
                          setCountryCode(code);
                        }}
                        showError={!!errors.phone}
                        errorText={errors.phone?.message}
                      />
                    )}
                  />

                  <Controller
                    name="dob"
                    control={control}
                    rules={{
                      required: translate('dateOfBirthIsRequired'),
                      validate: isPastDate,
                    }}
                    render={({ field }) => (
                      <CInput
                        title={translate('dateOfBirth')}
                        placeholderText={translate('selectDateOfBirth')}
                        Date
                        selectedDate={field.value}
                        onDateChange={field.onChange}
                        {...field}
                        mandatory
                        showError={!!errors.dob}
                        errorText={errors.dob?.message}
                      />
                    )}
                  />

                  <View style={styles.addressContainer}>
                    <SSNInput
                      control={control}
                      name="ssnLast4"
                      errorText={errors.ssnLast4?.message}
                      ref={ssnRef}
                      onSubmit={() => {
                        street1Ref?.current?.focus();
                      }}
                    />
                  </View>

                  <View style={styles.addressContainer}>
                    <Text style={styles.billingLabel}>{translate('billingAddress')}</Text>
                    <Text style={styles.subtext}>{translate('forPaymentsRelatedCommunication')}</Text>

                    <Controller
                      name="street1"
                      control={control}
                      rules={{ required: translate('streetAddressIsRequired') }}
                      render={({ field }) => (
                        <CInput
                          title={translate('streetAddress')}
                          placeholderText={translate('streetAddress1')}
                          {...field}
                          returnKeyType="next"
                          ref={street1Ref}
                          onSubmit={() => {
                            street2Ref?.current?.focus();
                          }}
                          mandatory
                          showError={!!errors.street1}
                          errorText={errors.street1?.message}
                        />
                      )}
                    />

                    <Controller
                      name="street2"
                      control={control}
                      render={({ field }) => (
                        <CInput
                          title={translate('streetAddress2')}
                          placeholderText={translate('streetAddress2')}
                          {...field}
                          returnKeyType="next"
                          ref={street2Ref}
                          onSubmit={() => {
                            cityRef?.current?.focus();
                          }}
                        />
                      )}
                    />

                    <Text style={styles.label}>{translate('state')}*</Text>
                    <Controller
                      name="state"
                      control={control}
                      rules={{ required: translate('selectState') }}
                      render={({ field }) => (
                        <View style={styles.dropdown}>
                          <DropdownList
                            data={states}
                            onSelect={item => field.onChange(item)}
                            selectedValue={field.value}
                            placeholder={translate('selectState')}
                            mandatory={true}
                          />
                          {errors.state && (
                            <Text style={styles.errorText}>{errors.state.message}</Text>
                          )}
                        </View>
                      )}
                    />

                    <Controller
                      name="city"
                      control={control}
                      rules={{ required: translate('cityIsRequired') }}
                      render={({ field }) => (
                        <CInput
                          title={translate('city')}
                          placeholderText={translate('enterCity')}
                          {...field}
                          returnKeyType="next"
                          ref={cityRef}
                          onSubmit={() => {
                            zipRef?.current?.focus();
                          }}
                          mandatory
                          showError={!!errors.city}
                          errorText={errors.city?.message}
                        />
                      )}
                    />

                    <Controller
                      name="zip"
                      control={control}
                      rules={{
                        required: translate('zipCodeIsRequired'),
                        pattern: {
                          value: /^\d{5}(-\d{4})?$/,
                          message: translate('enterAValidZipCode'),
                        },
                      }}
                      render={({ field }) => (
                        <CInput
                          title={translate('zipPostcode')}
                          placeholderText={translate('ex10001')}
                          keyboardType="number-pad"
                          {...field}
                          returnKeyType="done"
                          ref={zipRef}
                          mandatory
                          showError={!!errors.zip}
                          errorText={errors.zip?.message}
                        />
                      )}
                    />
                  </View>

                </AnimatedView>
              </ScrollView>
            </KeyboardAvoidingView>


            <View style={styles.footer}>
              <CButton loading={loader} onPress={handleSubmit(onSubmit)}>
                {translate('updateBankDetails')}
              </CButton>
            </View>
          </>
        )}
      </View>

      {/* <ModalComponent visible={modalVisible} setVisible={setModalVisible} type="bankVerified" /> */}
    </InsetWrapper>
  );
};

export default BankDetailsForm;
