/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable @typescript-eslint/no-shadow */
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Platform,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import { isEmpty } from '@app/utils/lodashFactions';
import { BaseColors, BaseStyles } from '@config/theme';
import FastImage from 'react-native-fast-image';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import NoRecord from '@components/NoRecord';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import SocketActions from '@redux/reducers/socket/actions';
import Header from '@components/Header';
import { translate } from '@language/Translate';
import TextInput from '@components/UI/TextInput';
import moment from 'moment';
import TypingIndicator from '@components/TypingIndicator/Index';
import { Images } from '@config/images';
import { FontFamily } from '@config/typography';
import AnimatedView from '@components/AnimatedView';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import { ChatLoader } from '@components/ContentLoader';
import { useAppDispatch, useRedux } from '@components/UseRedux';
import Toast from 'react-native-simple-toast';
import { navigationRef } from '@navigation/NavigationService';
import { getBatchCount } from '@app/utils/CommonFunction';

const {
  getChatList,
  setChatList,
  setTotalMsgCount,
  setSelectedRoom,
  emit,
  clearRecievedChatData,
} = SocketActions;

let valTimer: any = 0;

const ChatScreen = ({ navigation }: any) => {
  const dispatch = useAppDispatch();
  const { useAppSelector } = useRedux();

  // state
  const [searchChatList, setSearchChatList] = useState({
    page: 1,
    nextEnable: 0,
    data: {},
  });
  const [loading, setLoading] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );
  // const [batchCount, setBatchCount] = useState<any>(0);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'Seeker' | 'Employer'>(
    'Employer',
  );
  const [isTyping, setIsTyping] = useState(false);

  const [query, setQuery] = useState<string>('');
  const [unReadCount, setUnReadCount] = useState<any>({});

  // redux State
  const {
    chatRooms,
    chatLoader,
    chatListNextEnablePage,
    chatData,
    typingData,
    bottomLoader,
    totalMsgCount,
  } = useAppSelector((a: any) => a.socket);

  const { userData } = useAppSelector((a: any) => a.auth);

  dayjs.extend(utc);

  const isFocused = useIsFocused();

  useEffect(() => {
    getBatchCount();
    if (!isEmpty(totalMsgCount)) {
      setUnReadCount(totalMsgCount?.data || totalMsgCount);
      dispatch(
        emit('unread_message_count', { userId: userData?.id }, (res: any) => {
          console.log('unread_message_count resData ===>', res);
          if (res) {
            setUnReadCount(res);
            dispatch(setTotalMsgCount(res));
          }
        }),
      );
    } else {
      dispatch(
        emit('unread_message_count', { userId: userData?.id }, (res: any) => {
          console.log('unread_message_count resData ===>', res);
          if (res) {
            setUnReadCount(res);
            dispatch(setTotalMsgCount(res));
          }
        }),
      );
    }
    dispatch(
      getChatList(
        'init',
        '',
        1,
        '',
        chatRooms?.length > 0 ? false : true,
      ) as any,
    );
  }, [isFocused]);

  useFocusEffect(
    useCallback(() => {
      setSearchChatList({ page: 1, nextEnable: 0, data: {} });
      dispatch(setSelectedRoom({}));
    }, []),
  );

  // useEffect(() => {
  //   dispatch(getChatList('init', '', 1, '', true) as any);
  // }, []);

  useEffect(() => {
    if (chatData) {
      const fI = chatRooms.findIndex(
        (o: any) => o.id === chatData?.data?.data?.conversationRoomId,
      );
      if (fI !== -1) {
        const chatList = chatRooms[fI];
        chatRooms[fI] = {
          ...chatList,
          unReadMessageCount: chatList?.unReadMessageCount + 1,
          lastMessageTime: moment(),
        };
        dispatch(setChatList(chatRooms));
        dispatch(getChatList('init', '', 1, '', false) as any);

        const currentRoute = navigationRef?.current?.getCurrentRoute();
        if (currentRoute?.name === 'chat') {
          dispatch(clearRecievedChatData());
        }
      }
    }
  }, [chatData]);

  useEffect(() => {
    if (!isEmpty(typingData)) {
      const fObj = chatRooms?.find(
        (o: any) => o?.id === typingData?.data?.roomId,
      );
      if (!isEmpty(fObj)) {
        setIsTyping(fObj?.id);
      }
    } else {
      setIsTyping(false);
    }
  }, [typingData]);

  const handleSearchChatList = async (val: any, page = 1) => {
    setLoading(true);
    const options = {
      userId: userData?.id,
      // selectedTab,
      name: val,
    };
    dispatch(
      emit('search', options, (resData: any) => {
        if (resData?.status) {
          const sData = searchChatList?.data;
          setSearchChatList({
            ...searchChatList,
            // page,
            // nextEnable: resData?.data?.hasNextPage,
            data: page > 1 ? [...sData, ...resData?.data?.data] : resData?.data,
          });
          setLoading(false);
        } else {
          // Toast.show(
          //   isString(resData?.message)
          //     ? resData?.message || translate('searchChat')
          //     : translate('searchChat'),
          // );
          setSearchChatList([]);
          setLoading(false);
        }
      }),
    );
    setLoading(false);
  };

  const RenderDataItem = ({ item, navigation, index }: any) => {
    // Target time
    const targetTime = item?.lastMessageTime;

    // Parse target time
    const target = moment(targetTime);

    // Calculate time difference
    const diff = target.fromNow(true);
    const isAccountDeleted = item?.firstName === '' || !item?.firstName;
    return (
      <TouchableOpacity
        style={[styles.mainMsgView, { opacity: isAccountDeleted ? 0.5 : 1 }]}
        activeOpacity={isAccountDeleted ? 0.5 : 0.8}
        onPress={() => {
          // if (!isEmpty(isCurrentPlan)) {
          //   if (!blockScreen) {
          dispatch(setSelectedRoom(item));
          navigation.navigate('ChatDetails', {
            userInfo: { ...item },
          });
        }}>
        <View style={styles.renderItemMainView}>
          <View style={styles.dotMainView}>
            {item?.unReadMessageCount ? <View style={styles.dotView} /> : null}
          </View>
          <View style={styles.imgMainView}>
            <FastImage
              source={
                item?.imageUrl
                  ? { uri: item?.imageUrl || '' }
                  : item?.gender === 'female'
                    ? Images.female
                    : Images.user
              }
              resizeMode="cover"
              style={styles.imgView}
            />

            {/* green tick */}
            {item?.isOnline ? <View style={styles.greenDot} /> : null}
          </View>
          <View style={styles.nameTimeStyle}>
            {/* User Titles */}
            <View style={styles.titleView}>
              {/* {item?.userData?.username ? ( */}
              <Text numberOfLines={1} style={styles.userNameViewText}>
                {`${isAccountDeleted ? 'Account Deleted' : item?.firstName || '-'}`}
                {item?.jobTitle && (
                  <Text
                    numberOfLines={1}
                    style={{
                      ...styles.userNameViewText,
                      fontFamily: FontFamily.OpenSansBold,
                    }}>{` (${item?.jobTitle || ''})`}</Text>
                )}
              </Text>
            </View>
            {/* message Count */}
            <View style={styles.messageCount}>
              {isTyping === item?.id ? (
                <TypingIndicator type={'chatList'} />
              ) : (
                <Text
                  numberOfLines={1}
                  style={{
                    ...styles.messageCountText,
                    opacity: Number(item?.unReadMessageCount) === 0 ? 0.7 : 1,
                  }}>
                  {Number(item?.unReadMessageCount) > 99
                    ? '99+'
                    : Number(item?.unReadMessageCount) === 0
                      ? item?.lastMessage
                      : item?.unReadMessageCount || 0}
                  {Number(item?.unReadMessageCount) === 0
                    ? ''
                    : ' new messages'}
                </Text>
              )}
              {/* Data and time */}
              {item?.lastMessageTime ? (
                <Text style={styles.timeText}>{`${diff} ago`}</Text>
              ) : null}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const onRefresh = React.useCallback(() => {
    dispatch(getChatList('init', '', 1, '', true));
  }, []);

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    let bottom = true;
    if (isCloseToBottom && chatListNextEnablePage?.nextEnable) {
      dispatch(
        getChatList(
          'init',
          '',
          chatListNextEnablePage?.page + 1,
          '',
          false,
          bottom,
        ),
      );
    }
  };

  return (
    <View style={styles.main}>
      <Header
        leftIcon="logo"
        title={translate('msgs', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
        rightIcons={[
          {
            icon: 'notification1',
            onPress: () => {
              navigation.navigate('Notification');
            },
            notiBadge: true,
            wrapStyle: BaseStyles.notificationIconStyle,
          },
          {
            icon: 'reward-outlined',
            onPress: () => {
              navigation.navigate('RewardScreen');
            },
            // badge: totalMsgCount?.totalCount || false,
            // wrapStyle: styles.msgIconStyle,
          },
        ]}
      />

      <View style={styles.centerMain}>
        <TextInput
          onChange={(e: any) => {
            setQuery(e);

            if (valTimer) {
              clearTimeout(valTimer);
            }

            if (e?.length >= 2) {
              // Set a debounce timeout
              setTimeout(() => {
                handleSearchChatList(e, 1);
              }, 2000); // Adjust debounce time as needed (500ms is a common choice)
            } else if (e?.length === 0) {
              handleSearchChatList(e, 1);
            }
          }}
          value={query}
          placeholderText={translate('chatSearch', '')}
          searchButton={'chat'}
        />

        <ScrollView
          onScroll={handleScroll}
          scrollEventThrottle={0.5}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.primary]} // Customize refresh indicator color
              tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
            />
          }
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}>
          <AnimatedView>
            {/* <View style={styles?.tabComponantSty}>
              <TabComponent
                selectedTab={selectedTab}
                setSelectedTab={setSelectedTab}
                badge={unReadCount}
              />
            </View> */}
            {loading || chatLoader ? (
              <ChatLoader />
            ) : (
              <View style={styles.flatListContainer}>
                <AnimatedView>
                  <FlatList
                    data={query !== '' ? searchChatList?.data || [] : chatRooms}
                    renderItem={(item: any, index: any) => {
                      return (
                        <RenderDataItem
                          navigation={navigation}
                          item={item?.item}
                          index={index}
                        />
                      );
                    }}
                    showsVerticalScrollIndicator={false}
                    ListEmptyComponent={
                      <View style={styles.centerMain}>
                        <NoRecord
                          title={'inboxEmpty'}
                          type="chat"
                          description="reachOut"
                          iconName="employer"
                        />
                      </View>
                    }
                    ItemSeparatorComponent={<View style={styles.underLine} />}
                    style={{ marginBottom: Platform.OS === 'ios' ? 50 : 0 }}
                  />
                </AnimatedView>
                {bottomLoader ? (
                  <View style={{ marginBottom: 20 }}>
                    <ActivityIndicator
                      size="small"
                      color={BaseColors.primary}
                    />
                  </View>
                ) : null}
              </View>
            )}
          </AnimatedView>
        </ScrollView>
      </View>
    </View>
  );
};

export default ChatScreen;
