import Button from '@components/UI/Button';
import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { translate } from '@language/Translate';
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface EmptyGalleryProps {
  fromProfile?: boolean;
  handleUpload: () => void;
}

export default function EmptyGallery({ fromProfile = false, handleUpload }: EmptyGalleryProps) {
  return (
    <View
      style={styles.container}
      accessibilityLabel="Empty gallery"
    >
      <Text style={styles.title}>
        {fromProfile
          ? translate('emptyGalleryTitle')
          : translate('emptyGalleryWhenVisit')}
      </Text>

        {fromProfile ?
        <Text style={styles.description}>
            {translate('emptyGalleryDesc')}
        </Text> : null}

      {fromProfile && (
        <Button
        //   style={styles.button}
          onPress={handleUpload ?? (() => {})}
          type="primary"
        >
          {translate('uploadImages')}
        </Button>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 20,
    backgroundColor: BaseColors?.secondary,
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    marginTop: 10,
  },
  title: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors?.primary,
    marginBottom: 5,
    textAlign: 'center',
  },
  description: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansMedium,
    color: '#595959',
    marginBottom: 15,
    textAlign: 'center',
  },
  button: {
    backgroundColor: BaseColors?.primary,
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});
