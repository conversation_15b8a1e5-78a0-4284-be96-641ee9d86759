import { Dimensions, StyleSheet } from 'react-native';
import { BaseColors } from '../../config/theme';
import { FontFamily } from '../../config/typography';

const { width: SCREEN_WIDTH } = Dimensions.get('window');


export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  loader: { justifyContent: 'center' },
  profileContainer: {
    flex: 1,
    alignItems: 'center',
    // flexDirection: 'row',
    // paddingTop: 10,
    backgroundColor: '#FFF',
    // borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  fullWidth: {
    flex: 1,
    height: 32,
    justifyContent: 'center',
  },
  seekerFullWidth: {
    flex: 1, // Let it grow and take equal space
    height: 32,
    justifyContent: 'center',
  },
  seekerBtnFont: {
    fontSize: 14,
  },
  shareBtn: {
    height: 32,
    paddingHorizontal: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  skillView: {
    borderWidth: 1,
    borderColor: 'transparent',
    backgroundColor: BaseColors.skillColor,
    padding: 5,
    paddingHorizontal: 7,
    borderRadius: 10,
    paddingVertical: 5,
    marginVertical: 2,
    marginRight: 8,
    maxWidth: SCREEN_WIDTH * 0.22, // prevents big skills from breaking layout
  },
  bottomMsgBtn: {
    marginTop: 10,
    marginBottom: 10,
    backgroundColor: BaseColors.white,
  },
  seeMoreTxtSty: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    // textDecorationLine: 'underline',
    // paddingTop: 5,
  },
  moreSty: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.primary,
    textTransform: 'capitalize',
  },
  contentCard: {
    gap: 4,
    flex: 1,
  },
  mainNameView: {
    alignItems: 'center',
  },
  nameView: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 5,
    justifyContent: 'center',
  },
  locationView: {
    flexDirection: 'row',
    alignSelf: 'center',
    paddingTop: 0,
    width: '95%',
  },
  profileImageWrapper: {
    borderWidth: 1,
    width: 80,
    height: 80,
    borderRadius: 110 / 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: 'gray',
  },
  imageView: {
    width: 25,
    height: 25,
    marginLeft: 5,
  },

  medalIcon: {
    width: '100%',
    height: '100%',
  },
  profileImage: {
    width: 75,
    height: 75,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#DDD',
  },
  verificationBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,

    borderRadius: 12,
    padding: 3,
  },
  badgeText: {
    color: '#FFF',
    fontWeight: 'bold',
    fontSize: 10,
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    // marginTop: 10,
    color: BaseColors.textGrey,
    textTransform: 'capitalize',
    // flex: 1,
  },
  switchView: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    backgroundColor: BaseColors.inputBackground,
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 5,
    marginVertical: 5,
  },
  available: {
    marginLeft: 20,
  },
  icon: {
    marginLeft: 10,
  },
  status: {
    fontSize: 14,
    marginVertical: 5,
    color: '#666',
  },
  onlineDot: {
    color: '#4CAF50',
    fontSize: 16,
  },
  location: {
    color: BaseColors.placeHolderColor,
    // textDecorationLine: 'underline',
    fontSize: 14,
    marginLeft: 5,
    // marginTop: 5,
  },
  rating: {
    color: BaseColors.primary,
    fontSize: 14,
    marginVertical: 5,
  },
  skillsWrapper: {
    flexDirection: 'row',     // keep tags in one line
    justifyContent: 'flex-start',
    alignSelf: 'center',
    flexWrap: 'nowrap',       // disable wrapping
    marginTop: 10,
    marginBottom: 5,
  },
  skillBadge: {
    backgroundColor: '#EFEFEF',
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 15,
    marginHorizontal: 5,
  },
  skillText: {
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
    paddingBottom: 2,
    fontSize: 12,
  },
  description: {
    fontSize: 15,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    flexDirection: 'row', // Ensure text and link appear on the same row
    flexWrap: 'wrap', // Allow wrapping when content overflows
    textAlign: 'center',
    // textTransform: 'capitalize',
  },
  descriptionTxt: {
    fontSize: 15,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    flexDirection: 'row', // Ensure text and link appear on the same row
    flexWrap: 'wrap', // Allow wrapping when content overflows
    textAlign: 'center',
  },
  availableTxtSty: {
    fontSize: 12,
    marginVertical: 5,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textColor,
    // textTransform: 'capitalize',
  },
  messageButton: {
    backgroundColor: '#007BFF',
    padding: 10,
    borderRadius: 5,
    marginVertical: 10,
  },
  messageButtonText: {
    color: '#FFF',
    fontWeight: 'bold',
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
  },
  sectionContainer: {
    padding: 15,
    backgroundColor: '#F5FAFF',
    marginTop: 0,
    borderRadius: 10,
    // marginHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 16,
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansBold,
    marginBottom: 7,
  },
  card: {
    marginBottom: 10,
    paddingVertical: 5,
    borderRadius: 5,
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginBottom: 10,
  },
  cardIcon: {
    width: 40,
    height: 40,
    marginRight: 10,
    borderRadius: 20,
  },
  badgeIcon: {
    marginLeft: 'auto',
  },
  cardTitle: {
    fontSize: 13,
    fontFamily: FontFamily.OpenSansBold,
  },
  cardSubtitle: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
  },
  cardDescription: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
    marginVertical: 5,
    // marginLeft: 52,
  },
  placeholderImage: {
    width: 80,
    height: 80,
    backgroundColor: '#EFEFEF',
    marginTop: 10,
    alignSelf: 'center',
  },
  moreLink: {
    color: BaseColors.primary,
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    marginTop: 10,
    textAlign: 'center',
    textTransform: 'capitalize',
  },
  underlineViewSty: {
    borderBottomWidth: 0.7,
    borderBottomColor: BaseColors.activeColor,
  },
  msgIconStyle: {
    right: 0,
    width: 35,
    height: 35,
    // borderWidth: 1,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColors.primary,
  },
  proBadge: {
    backgroundColor: BaseColors.activeColor,
    color: BaseColors.primary,
    fontSize: 10,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginLeft: 5,
    padding: 3,
  },
  levelBadge: {
    backgroundColor: BaseColors.activeColor,
    color: BaseColors.primary,
    fontSize: 10,
    borderRadius: 5,
    paddingHorizontal: 5,
    marginLeft: 5,
    paddingTop: 3,
  },
  approveBtn: {
    backgroundColor: BaseColors.completedColor,
    width: '47%',
  },
  declineBtn: {
    borderColor: BaseColors.btnRed,
    width: '47%',
  },
  applicationsBtns: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginHorizontal: 20,
    marginBottom: 10,
    marginTop: 20,
    gap: 5,
  },
  reviewViewSty: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 0,
    justifyContent: 'center',
  },
  ratingTxtSty: {
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansRegular,
    paddingHorizontal: 4,
  },
  marginTop20: {
    marginTop: 20,
  },
  marginTop: {
    marginTop: 5,
  },
  isAvailableTxtSty: {
    marginTop: 10,
    fontSize: 14,
    fontFamily: FontFamily?.OpenSansRegular,
    color: BaseColors?.textColor,
    // textAlign: 'center',
  },
  switchSty: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  descriptionContainer: {
    flexDirection: 'row', // Align text and link on the same row
    // flexWrap: 'wrap', // Wrap content if it's too long
    alignItems: 'center',
  },
  toggleText: {
    color: BaseColors.primary,
    fontFamily: FontFamily?.OpenSansRegular,
    fontSize: 14,
  },
  revieeDataSty: {
    backgroundColor: BaseColors.inputBackground,
    marginVertical: 10,
    padding: 15,
    borderRadius: 10,
  },
  seeMoreView: {
    alignSelf: 'center',
    paddingTop: 5,
  },
});
