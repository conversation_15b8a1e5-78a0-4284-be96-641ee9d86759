import {
  Alert,
  LayoutAnimation,
  Linking,
  PermissionsAndroid,
  Platform,
  Share,
  UIManager,
} from 'react-native';
import authActions from '../redux/reducers/auth/actions';
import userConfigActions from '../redux/reducers/userConfig/actions';
import Geolocation from 'react-native-geolocation-service';
import DeviceInfo from 'react-native-device-info';
import { store } from '../redux/store/configureStore';
import { initTranslate, translate } from '../lang/Translate';
import { EventRegister } from 'react-native-event-listeners';
import * as Sentry from '@sentry/react-native';
import { has, isObject } from '@app/utils/lodashFactions';
import FileViewer from 'react-native-file-viewer';
import { fileTypes } from '@screens/Settings/staticData';
import BaseSetting from '@config/setting';
import { getApiData } from './apiHelper';
import Toast from 'react-native-simple-toast';
import RNFS from 'react-native-fs';
import { navigationRef } from '@navigation/NavigationService';
import { CommonActions } from '@react-navigation/native';
import { Images } from '@config/images';
import Clipboard from '@react-native-clipboard/clipboard';
import { deleteFcmToken } from '@components/Common/PushNotification';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import socketActions from '@redux/reducers/socket/actions';
import notificationActions from '@redux/reducers/notification/actions';
import actions from '../redux/reducers/auth/actions';
import { check, PERMISSIONS, request, RESULTS } from 'react-native-permissions';

dayjs.extend(duration);

export const enableAnimateInEaseOut = () => {
  if (
    Platform.OS === 'android' &&
    UIManager &&
    UIManager.setLayoutAnimationEnabledExperimental
  ) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
  LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
};

// Initialize react-native-reanimated properly for Android
export const initializeReanimated = () => {
  if (Platform.OS === 'android') {
    // Ensure Reanimated is properly initialized on Android
    // This helps prevent blank screen issues with animations
    if (UIManager && UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }

    // Add extra timeout to ensure proper initialization in Android
    setTimeout(() => {
      // Force a small layout change to trigger proper initialization
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    }, 300);
  }
};

export const getNoDataImage = (type: string) => {
  const noDataImages: Record<string, any> = {
    chat: Images.newConversation,
    Employer: Images.noJobsAvailable,
    Seeker: Images.noJobSeeker,
    noJobsFound: Images.jobNotFound,
  };

  return noDataImages[type] || null; // Return `null` or a default image if `type` is not found
};

export const getCountryFlagCode = countryCode => {
  const countryCodeToFlagMap = {
    '1': 'US', // United States/Canada
    '44': 'GB', // United Kingdom
    '91': 'IN', // India
    '61': 'AU', // Australia
    '49': 'DE', // Germany
    '33': 'FR', // France
    '39': 'IT', // Italy
    '81': 'JP', // Japan
    '86': 'CN', // China
    '7': 'RU', // Russia
    '34': 'ES', // Spain
    '55': 'BR', // Brazil
    '52': 'MX', // Mexico
    '82': 'KR', // South Korea
    '31': 'NL', // Netherlands
    '46': 'SE', // Sweden
    '41': 'CH', // Switzerland
    '64': 'NZ', // New Zealand
    '92': 'PK', // Pakistan
    '971': 'AE', // United Arab Emirates
    '966': 'SA', // Saudi Arabia
    '65': 'SG', // Singapore
  };

  return countryCodeToFlagMap[countryCode] || null;
};

// Usage
const countryCode = '91';
const flagCode = getCountryFlagCode(countryCode); // Returns 'IN'

export const getFormattedAddress = data => {
  let city = '';
  let state = '';
  let country = '';

  if (data?.address_components) {
    data.address_components.forEach(component => {
      if (component.types.includes('locality')) {
        city = component.short_name;
      }
      if (component.types.includes('administrative_area_level_1')) {
        state = component.short_name;
      }
      if (component.types.includes('country')) {
        country = component.long_name; // Use long name for a full country name
      }
    });
  }

  // If city and state exist, return "City, State"; otherwise, return just the country
  if (city && state) {
    return `${city}, ${state}`;
  } else if (state) {
    return state; // Show state alone if no city
  } else {
    return country; // Show country if no city or state
  }
};

export const capitalizeFirstLetterOfEachWord = (str: any) => {
  if (!str) {
    return '';
  }
  return str
    .split(' ') // Split the string into words
    .map(
      (word: any) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
    ) // Capitalize first letter of each word
    .join(' '); // Join the words back together
};

export const getBadgeImage = (badge: string) => {
  const badgeImages: Record<string, any> = {
    Bronze: Images.bronze,
    Silver: Images.silver,
    Gold: Images.gold,
    Platinum: Images.platinum,
    Diamond: Images.Diamond,
  };

  return badgeImages[badge] || Images.bronze;
};

const clearStorage = () => {
  store.dispatch(authActions.setUserData({}) as any);
  store.dispatch(authActions.setUserProfileData({}) as any);
  store.dispatch(authActions.setReawrdModal({ visible: false, data: {} }) as any);
  store.dispatch(authActions.setWalkthroughVisible(false) as any);
  store.dispatch(authActions.setAccessToken('') as any);
  store.dispatch(authActions.setSelectedHomeTab('') as any);
  store.dispatch(userConfigActions.clearData() as any);
  store.dispatch(socketActions.setChatList([]) as any);
  store.dispatch(socketActions.setTotalMsgCount(0) as any);
  store.dispatch(socketActions.setSelectedRoom({}) as any);
  store.dispatch(socketActions.clearRecievedChatData() as any);
};

export const updateUserData = async (userId: number, type: string = '') => {
  try {
    const resp = await getApiData({
      endpoint: `${BaseSetting.endpoints.usersList}`,
      method: 'GET',
      data: { userId, page: 1 },
    });
    console.log('updateUserData', resp?.data, userId);
    if (resp?.data && resp?.status && resp?.data?.id) {
      // const isProfileSetupOngoing = store.getState().auth?.userData?.isProfileSetupOngoing;
      const d = resp?.data;
      store.dispatch({
        type: actions.SET_USER_DATA,
        userData: d,
      });
      if (type === 'EntryPoints') {
        updateReview('daily_login', resp?.data?.id || userId);
      }
    } else {
      // Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
    }
  } catch (error) {
    console.error('Error fetching list:', error);
    // Toast.show('Failed to fetch data.', Toast.SHORT);
  }
};
export const resetStackScreen = (screen: string) => {
  const current = navigationRef?.current?.getCurrentRoute();
  if (current?.name === screen) {
    return;
  }

  navigationRef?.current?.dispatch(
    CommonActions.reset({
      index: 0,
      routes: [{ name: screen }],
    }),
  );
};

export const logOutCall = async (type?: string) => {
  if (type === 'deleteAccount') {
    clearStorage();
    deleteFcmToken();
    resetStackScreen('BottomTabsNavigator');
  }
  try {
    const {
      notification: { fcmToken },
    } = store.getState();
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.logout,
      method: 'GET',
      data: { fcmToken, fcmPlatform: isIOS() ? 'ios' : 'android' },
    });
    if (resp?.data && resp?.status) {
      clearStorage();
    } else {
      clearStorage();
      // Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
    }
    deleteFcmToken();
    resetStackScreen('BottomTabsNavigator');
  } catch (e) {
    resetStackScreen('BottomTabsNavigator');
    clearStorage();
    console.log('ERRR', e);
  }
};

// **************Function to handle notification on click*********************8888888

export const callCommonHandleNotificationfunction = async (
  notification: any,
) => {
  const {
    auth: { accessToken },
  } = store.getState();
  console.log('SHUTTER CLICK', notification);

  const extraData = JSON.parse(notification?.data?.extraData);
  setTimeout(() => { }, 2500);
};

export const handleNotification = async (notification: any) => {
  console.log('LOCAL NOTIFICATION ==>', JSON.stringify(notification));
  const {
    auth: { accessToken },
  } = store.getState();
};

const changeLanguage = (newLang: any, newJson: any) => {
  initTranslate(store, true);
};

export const handleAvailabilityUpdate = async (
  key: string,
  value: any,
  onSuccess?: (res: any) => void,
  onError?: (err: any) => void,
) => {
  const data = {
    [key]: value,
  };

  try {
    const res = await getApiData({
      endpoint: BaseSetting.endpoints.checkIsAvailable, // fixed endpoint
      method: 'POST',
      data,
    });

    if (res?.status === true) {
      onSuccess?.(res);
    } else {
      onError?.(res);
    }
  } catch (err) {
    onError?.(err);
  }
};

export const calculateTotalSalary = (
  start: string | null,
  end: string | null,
  timeStart: string | null,
  timeEnd: string | null,
  salary: string,
  duration: string,
): any => {
  if (!start || !end || !salary || !duration) {
    return 0;
  }

  try {
    const startDateObj = new Date(start);
    const endDateObj = new Date(end);
    const dateDiff = Math.ceil(
      (endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24) +
      1,
    );

    const salaryPerUnit = parseFloat(salary);

    switch (duration) {
    case 'Per hour': {
      if (timeStart && timeEnd) {
        const startTimeObj = new Date(timeStart);
        const endTimeObj = new Date(timeEnd);

        const startHours = startTimeObj.getUTCHours();
        const endHours = endTimeObj.getUTCHours();
        const startMinutes = startTimeObj.getUTCMinutes();
        const endMinutes = endTimeObj.getUTCMinutes();

        let timeDiff = endHours - startHours;
        timeDiff += (endMinutes - startMinutes) / 60;

        const absoluteTimeDiff = Math.abs(timeDiff);
        const totalSalary = dateDiff * absoluteTimeDiff * salaryPerUnit;

        return Math.round(totalSalary);
      }
      break;
    }

    case 'Per day': {
      const totalSalary = dateDiff * salaryPerUnit;
      return Math.round(totalSalary);
    }

    case 'Per week': {
      // For example: if salary is 1 per week
      // 2 weeks and 2 days = 2 + (1/7 * 2) = 2.285714...
      const fullWeeks = Math.floor(dateDiff / 7);
      const remainingDays = dateDiff % 7;
      const weekSalary = fullWeeks * salaryPerUnit;
      const daySalary = (salaryPerUnit / 7) * remainingDays;
      const totalSalary = weekSalary + daySalary;
      return Math.round(totalSalary * 100000) / 100000; // Keep 5 decimal places
    }

    case 'Per month': {
      // For example: if salary is 1 per month
      // 2 months and 2 days = 2 + (1/30 * 2) = 2.066666...
      const startYear = startDateObj.getFullYear();
      const startMonth = startDateObj.getMonth();
      const endYear = endDateObj.getFullYear();
      const endMonth = endDateObj.getMonth();

      let months = (endYear - startYear) * 12 + (endMonth - startMonth);
      const remainingDays = endDateObj.getDate() - startDateObj.getDate();

      if (remainingDays < 0) {
        months -= 1;
        const daysInLastMonth = new Date(endYear, endMonth, 0).getDate();
        const actualRemainingDays = daysInLastMonth + remainingDays;
        const monthSalary = months * salaryPerUnit;
        const daySalary = (salaryPerUnit / 30) * actualRemainingDays;
        const totalSalary = monthSalary + daySalary;
        console.log(
          'daysInLastMonth ==>',
          duration,
          daysInLastMonth,
          actualRemainingDays,
          monthSalary,
          daySalary,
          totalSalary,
        );
        return (Math.round(totalSalary * 100000) / 100000).toFixed(2);
      } else {
        const monthSalary = months * salaryPerUnit;
        const daySalary = (salaryPerUnit / 28) * remainingDays;
        const totalSalary = monthSalary + daySalary;
        console.log(
          'daysInLastMonth  else ==>',
          duration,
          months,
          remainingDays,
          monthSalary,
          daySalary,
          totalSalary,
        );
        return (Math.round(totalSalary * 100000) / 100000).toFixed(2);
      }
    }

    case 'Per year': {
      // For example: if salary is 1 per year
      // 2 years and 2 days = 2 + (1/365 * 2) = 2.005479...
      const years = endDateObj.getFullYear() - startDateObj.getFullYear();
      const remainingDays = Math.floor(
        (endDateObj -
            new Date(
              endDateObj.getFullYear(),
              startDateObj.getMonth(),
              startDateObj.getDate(),
            )) /
          (1000 * 60 * 60 * 24),
      );

      const yearSalary = years * salaryPerUnit;
      const daySalary = (salaryPerUnit / 365) * remainingDays;
      const totalSalary = yearSalary + daySalary;
      return Math.round(totalSalary * 100000) / 100000;
    }

    default:
      return 0;
    }
  } catch (error) {
    console.error('Error in salary calculation:', error);
    return 0;
  }
};

export const getDuration = (type: string) => {
  const durationMap: { [key: string]: string } = {
    perHour: 'hr',
    perMonth: 'mo',
    perYear: 'yr',
    perDay: 'day',
    perWeek: 'wk',
  };
  return durationMap[type] || ''; // Default to 'wk' if type is not found
};

export const formatDateTime = (date: any, format = 'date') => {
  const formatObject: any = {
    time: 'h:mm A',
    date: 'MMM D, YYYY',
    newFormat: 'M/D/YYYY',
    dateInAPI: 'MM/DD/YYYY',
    dateTime: 'MMM D, YYYY h:mm A',
    timeAPI: 'hh:mm A',
  };

  const formatString = formatObject[format];

  return dayjs(date).format(formatString);
};

export const getDurationLabel = (duration?: string): string => {
  const durationMap: Record<string, string> = {
    flatRate: 'Flat Rate',
    perDay: 'Per day',
    perMonth: 'Per month',
    perHour: 'Per hour',
    perWeek: 'Per week',
  };

  return duration ? durationMap[duration] || '' : '';
};

export const convertToCamelCase = (text?: string): string => {
  if (!text) {
    return '';
  }

  return text
    .toLowerCase()
    .split(' ')
    .map((word, index) =>
      index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1),
    )
    .join('');
};

export const handleThemeMode = (d: any) => {
  return new Promise(async () => {
    try {
      if (EventRegister) {
        EventRegister.emit('changeAppThemeMode', d);
      }
      store.dispatch(authActions.setDarkmode(d));
    } catch (error) { }
  });
};

export function chatFilesVal(type: any, size: any) {
  const fTypes = isObject(fileTypes) ? fileTypes : {};
  if (has(fTypes, type)) {
    if (size > 1024 * 1024 * 10) {
      return false;
    } else {
      return true;
    }
  }
}

/**
 * Common function to update reviews.
 * @param {string} slug - The dynamic slug to pass.
 * @param {number} userId - The user's ID.
 * @returns {Promise<boolean>} - Returns true if successful, false otherwise.
 */
export const updateReview = async (
  slug: string,
  userId: number,
  jobId?: number,
): Promise<{ status: boolean; badgeInfo?: any }> => {
  const newObj = { userId, slug, jobId };
  try {
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.updateReward,
      method: 'POST',
      data: newObj,
    });

    if (resp?.status) {
      return { status: true, badgeInfo: resp?.badgeInfo };
    } else {
      // Toast.show(resp?.message, Toast.BOTTOM);
      return { status: false };
    }
  } catch (error) {
    return { status: false };
  }
};

export const copyToClipBoard = (text: string) => {
  Clipboard.setString(text);
  Toast.show(translate('textCopied'), Toast.LONG);
};

const requestStoragePermission = async () => {
  if (Platform.OS === 'android') {
    let androidVersion = DeviceInfo.getSystemVersion();
    // const granted = await PermissionsAndroid.request(
    //   PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
    // );
    const granted = await PermissionsAndroid.request(
      parseInt(androidVersion) >= 13
        ? PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
        : PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
    );
    console.log('granted ==>', granted);
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('Storage Permission Granted ✅');
      return true;
    } else if (granted === PermissionsAndroid.RESULTS.DENIED) {
      console.log('Storage Permission Denied ❌');
      Toast.show('You need to allow storage permission to download files.');
      return false;
    } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
      console.log('Storage Permission Denied with \'Never Ask Again\' ❌🚫');
      Alert.alert(
        'Storage Permission Required',
        'Please enable storage permission in settings to download files.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Open Settings', onPress: () => Linking.openSettings() },
        ],
      );

      return false;
    }
    console.log('granted ==>', granted, PermissionsAndroid.RESULTS.GRANTED);
    // return granted === PermissionsAndroid.RESULTS.GRANTED;
  }
  return true; // iOS doesn't need permission for DocumentsDirectory
};

export const downloadFile = async (fileUrl: string) => {
  try {
    const hasPermission = await requestStoragePermission();
    if (!hasPermission) {
      Toast.show(
        'Storage permission is required to download files.',
        Toast.LONG,
      );
      return;
    }

    const fileName = `Invoice-${Math.random()}`;
    // File path where the file will be saved
    const path = isIOS()
      ? `${RNFS.DocumentDirectoryPath}/${fileName}.pdf` // iOS: Save in app's document folder
      : `${RNFS.DownloadDirectoryPath}/${fileName}.pdf`; // Android: Save in Downloads folder

    console.log(
      'RNFS.DownloadDirectoryPath ===>',
      fileUrl,
      hasPermission,
      RNFS.DownloadDirectoryPath,
      RNFS.DocumentDirectoryPath,
    );

    // Download file
    const response = await RNFS.downloadFile({
      fromUrl: fileUrl,
      toFile: path,
      background: true,
      progress: (res: any) => {
        const progress = (res.bytesWritten / res.contentLength) * 100;
        console.log(`Download Progress: ${progress.toFixed(2)}%`);
      },
      begin(res) {
        Toast.show('Downloading started...', Toast.LONG);
      },
    }).promise;

    if (response.statusCode === 200) {
      if (isIOS()) {
        // Open file
        FileViewer.open(path)
          .then(() => console.log('File opened successfully'))
          .catch(error => {
            console.error('Error opening file:', error);
            Alert.alert('Error', 'Cannot open this file.');
          });
      } else {
        Toast.show(`File saved: ${fileName}.pdf`, Toast.LONG);
      }
      console.log('res -> ', JSON.stringify(response));
    } else {
      Toast.show('Download Failed', Toast.LONG);
    }
  } catch (error) {
    console.error(error);
    Toast.show('Download failed. Please try again.', Toast.LONG);
  }
};

export const getSettings = async (): Promise<any> => {
  try {
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.getSettings,
      method: 'GET',
      data: {},
    });

    console.log('resp ===>', resp);

    if (resp?.status) {
      console.log('🚀 ~ getSettings ~ resp:', resp);
      return resp;
    } else {
      // Toast.show(resp?.message, Toast.BOTTOM);
      return { status: false, message: resp?.message };
    }
  } catch (error) {
    // Toast.show('Something went wrong', Toast.BOTTOM);
    return { status: false };
  }
};

export const getSystemSettings = async (): Promise<any> => {
  try {
    const resp = await getApiData({
      endpoint: BaseSetting.endpoints.getSystemSettings,
      method: 'GET',
      data: {},
    });

    console.log('resp ===>', resp);

    if (resp?.status) {
      return resp;
    } else {
      // Toast.show(resp?.message, Toast.BOTTOM);
      return { status: false, message: resp?.message };
    }
  } catch (error) {
    // Toast.show('Something went wrong', Toast.BOTTOM);
    return { status: false };
  }
};

export const capitalizeFirstLetter = (text: any) => {
  return text?.charAt(0)?.toUpperCase() + text?.slice(1);
};

export async function handleFilePick(file: any, type: string) {
  try {
    const fileSize = file.size || file?.fileSize;

    if (fileSize && fileSize > maxFileSize) {
      Toast.show('File size must be less than 10MB.', Toast.LONG);
      return;
    }
    const fileData = {
      type: file.type || file.mime,
      name:
        file.name ||
        file?.path?.substring(file?.path?.lastIndexOf('/') + 1) ||
        '',
      uri: file.uri || file.path,
    };

    const url = BaseSetting.endpoints.uploadDocument;
    const imgFile = { file: fileData }; // Pass array to the API

    const res = await getApiData({
      endpoint: url,
      method: 'POST',
      data: imgFile,
      formData: true,
    });
    if (res?.status) {
      // Toast.show(res?.message, Toast.BOTTOM);

      // if (type === 'licence') {
      //   setLicenseFiles((prevFiles: any) => [
      //     ...prevFiles,
      //     {
      //       fileName: uploadedFile.fileName,
      //       filePath: uploadedFile.filePath,
      //       fileSize: uploadedFile.fileSize,
      //     },
      //   ]);
      // } else if (type === 'cv') {
      //   setCvFile(res?.data);
      // } else if (type === 'image') {
      //   // setImageName(res?.data?.file_name);
      //   // setProfileImage(file?.path);
      // }
      return res;
    } else {
      // console.log(
      //   'response Error ==>',
      //   res,
      //   String(res?.data?.message)?.includes('Network'),
      // );
      // const isNetworkIssue = String(res)?.includes('Network Error');
      // if (isNetworkIssue) {
      //   handleFilePick(file, type);
      // }
      Toast.show(res?.message || String(res?.data?.message), Toast.LONG); // Error for upload failure
    }
  } catch (error) {
    const isNetworkIssue = error?.includes('Network');
  }
}

const firebaseAuthErrorMap: Record<string, string> = {
  'auth/invalid-phone-number': 'The phone number is invalid.',
  'auth/missing-phone-number': 'Phone number is required.',
  'auth/too-many-requests': 'Too many attempts. Please wait a few minutes and try again.',
  'auth/quota-exceeded': 'SMS quota exceeded. Please try again later.',
  'auth/network-request-failed': 'Network error. Please check your internet connection.',
  'auth/user-disabled': 'This user account has been disabled.',
  'auth/invalid-verification-code': 'The OTP you entered is incorrect. Please try again.',
  'auth/missing-verification-code': 'Please enter the OTP you received.',
  'auth/invalid-verification-id': 'Verification session expired. Request a new OTP.',
  'auth/missing-verification-id': 'No verification session found. Please request a new OTP.',
  'auth/session-expired': 'The OTP has expired. Please request a new one.',
  'auth/invalid-credential': 'Verification failed. Please try again.',
  'auth/credential-already-in-use': 'This phone number is already linked to another account.',
  'auth/app-not-authorized': 'This app is not authorised to use Firebase Auth.',
  'auth/operation-not-allowed': 'Phone sign-in is not enabled in Firebase settings.',
  'auth/invalid-app-credential': 'App verification failed. Reinstall the app and try again.',
};

export function getFBErrorMessage(error: any): string {
  if (!error || typeof error !== 'object') {return 'Something went wrong. Please try again later.';}
  return firebaseAuthErrorMap[error.code] || error.message || 'Something went wrong. Please try again later.';
}

export const checkImageExists = async (url: string) => {
  try {
    const response = await fetch(url, { method: 'HEAD' });

    if (!response.ok) {
      return false;
    } // URL not accessible

    const contentType = response.headers.get('content-type');

    return contentType && contentType.startsWith('image/');
  } catch (error) {
    console.error('Error checking image:', error);
    return false;
  }
};
export async function handleJobFilePick(file: any, type: string) {
  try {
    const fileData = {
      type: file.type || file.mime,
      name:
        file.name ||
        file?.path?.substring(file?.path?.lastIndexOf('/') + 1) ||
        '',
      uri: file.uri || file.path,
    };

    const url = BaseSetting.endpoints.uploadjobFile;
    const imgFile = { file: fileData }; // Pass array to the API

    const res = await getApiData({
      endpoint: url,
      method: 'POST',
      data: imgFile,
      formData: true,
    });
    if (res?.status) {
      // Toast.show(res?.message, Toast.BOTTOM);

      // if (type === 'licence') {
      //   setLicenseFiles((prevFiles: any) => [
      //     ...prevFiles,
      //     {
      //       fileName: uploadedFile.fileName,
      //       filePath: uploadedFile.filePath,
      //       fileSize: uploadedFile.fileSize,
      //     },
      //   ]);
      // } else if (type === 'cv') {
      //   setCvFile(res?.data);
      // } else if (type === 'image') {
      //   // setImageName(res?.data?.file_name);
      //   // setProfileImage(file?.path);
      // }
      return res;
    } else {
      // console.log(
      //   'response Error ==>',
      //   res,
      //   String(res?.data?.message)?.includes('Network'),
      // );
      // const isNetworkIssue = String(res)?.includes('Network Error');
      // if (isNetworkIssue) {
      //   handleFilePick(file, type);
      // }
      Toast.show(res?.message || String(res?.data?.message), Toast.LONG); // Error for upload failure
    }
  } catch (error) {
    const isNetworkIssue = error?.includes('Network');
  }
}
export async function handleUploadJobFile(file: any, type: string) {
  try {
    const fileData = {
      type: file.type || file.mime,
      name:
        file.name ||
        file?.path?.substring(file?.path?.lastIndexOf('/') + 1) ||
        '',
      uri: file.uri || file.path,
    };

    const url = BaseSetting.endpoints.uploadjobFile;
    const imgFile = { file: fileData }; // Pass array to the API

    const res = await getApiData({
      endpoint: url,
      method: 'POST',
      data: imgFile,
      formData: true,
    });
    if (res?.status) {
      // Toast.show(res?.message, Toast.BOTTOM);

      // if (type === 'licence') {
      //   setLicenseFiles((prevFiles: any) => [
      //     ...prevFiles,
      //     {
      //       fileName: uploadedFile.fileName,
      //       filePath: uploadedFile.filePath,
      //       fileSize: uploadedFile.fileSize,
      //     },
      //   ]);
      // } else if (type === 'cv') {
      //   setCvFile(res?.data);
      // } else if (type === 'image') {
      //   // setImageName(res?.data?.file_name);
      //   // setProfileImage(file?.path);
      // }
      return res;
    } else {
      // console.log(
      //   'response Error ==>',
      //   res,
      //   String(res?.data?.message)?.includes('Network'),
      // );
      // const isNetworkIssue = String(res)?.includes('Network Error');
      // if (isNetworkIssue) {
      //   handleFilePick(file, type);
      // }
      Toast.show(res?.message || String(res?.data?.message), Toast.LONG); // Error for upload failure
    }
  } catch (error) {
    const isNetworkIssue = error?.includes('Network');
  }
}

export const requestPermissions = async () => {
  return new Promise(async (resolve, reject) => {
    try {
      const hasPermission = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
      );
      if (hasPermission) {
        resolve(true);
      } else {
        const result = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        ]);
        if (
          result['android.permission.READ_EXTERNAL_STORAGE'] !== 'granted' &&
          result['android.permission.WRITE_EXTERNAL_STORAGE'] !== 'granted' &&
          result['android.permission.RECORD_AUDIO'] !== 'granted'
        ) {
          reject(new Error('Permissions not granted'));
        }
      }
    } catch (err) {
      console.warn(err);
      reject(err);
    }
  });
};
// commonFunctions.js

export const getTimeAgo = (dateString: any) => {
  const createdAt = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now - createdAt) / 1000);

  if (diffInSeconds < 10) {
    return 'few seconds ago';
  }
  if (diffInSeconds < 60) {
    return `${diffInSeconds} seconds ago`;
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minutes ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hours ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays} days ago`;
};

export const formatSalary = (amount: any) => {
  if (!amount) {
    return '';
  } // Return an empty string if amount is undefined or null
  if (amount >= 100000) {
    return `${(amount / 100000).toFixed(1)}L`; // Format for lakhs
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}k`; // Format for thousands
  }
  return amount.toString(); // Return the original value for less than 1000
};

export const isIOS = () => {
  return Platform.OS === 'ios';
};

// Let's say this function is invoked when a user clicks on the checkout button of your shop
export function shopCheckout() {
  // This will create a new Transaction for you
  const transaction = Sentry.startTransaction({ name: 'shopCheckout' });
  // Set transaction on scope to associate with errors and get included span instrumentation
  // If there's currently an unfinished transaction, it may be dropped
  Sentry.getCurrentHub().configureScope(scope => scope.setSpan(transaction));

  // Assume this function makes an xhr/fetch call
  // const result = validateShoppingCartOnServer();

  // const span = transaction.startChild({
  //   data: {
  //     result
  //   },
  //   op: 'task',
  //   description: "processing shopping cart result",
  // });
  try {
    // processAndValidateShoppingCart(result);
    // span.setStatus(SpanStatus.Ok);
  } catch (err) {
    // span.setStatus(SpanStatus.UnknownError);
    throw err;
  } finally {
    // span.finish();
    transaction.finish();
  }
}

// Function to format the date
export const formatDate = (isoDate: any) => {
  // const date = new Date(isoDate);
  // const day = String(date.getDate()).padStart(2, '0'); // Ensure two digits
  // const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-based
  // const year = date.getFullYear();

  return dayjs(isoDate).format('MM/DD/YYYY');

  // return `${month}/${day}/${year}`;
};

export function toCamelCase(str: string) {
  return str
    .toLowerCase() // Convert the entire string to lowercase
    .split(/\s+/) // Split by one or more spaces
    .map((word: string, index: number) =>
      index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1),
    ) // Capitalize each word except the first
    .join(''); // Join them together
}

export const calculateDuration = (
  startDate: any,
  endDate: any,
  startTime: any,
  endTime: any,
  type: string,
) => {
  const startDateMoment = dayjs(startDate).startOf('day');
  const endDateMoment = dayjs(endDate).startOf('day');
  const startTimeMoment = dayjs(startTime);
  const endTimeMoment = dayjs(endTime);

  // Calculate daily working hours
  let dailyHours = dayjs
    .duration(endTimeMoment.diff(startTimeMoment))
    .asHours();
  if (dailyHours < 0) {
    dailyHours += 24; // Adjust for time spanning past midnight
  }

  // Type-based calculations
  switch (type) {
  case 'Per hour': {
    const totalDays = endDateMoment.diff(startDateMoment, 'days') + 1; // Include both start and end date
    const totalHours = totalDays * dailyHours;
    return `${Math.round(totalHours)} hours`;
  }
  case 'Per day': {
    const totalDays = endDateMoment.diff(startDateMoment, 'day') + 1; // Include both start and end date
    return `${totalDays} ${totalDays > 1 ? 'days' : 'day'}`;
  }
  case 'Per week': {
    const totalDays = endDateMoment.diff(startDateMoment, 'days') + 1; // Include both start and end date
    const totalWeeks = Math.floor(totalDays / 7); // Full weeks
    const remainingDays = totalDays % 7; // Remaining days
    return remainingDays
      ? `${totalWeeks * 7 + remainingDays} days`
      : `${totalWeeks} ${totalWeeks === 1 ? 'wk' : 'wks'}`;
  }
  case 'Per month': {
    const totalMonths = endDateMoment.diff(startDateMoment, 'months', true); // Fractional months
    const roundedMonths = Math.floor(totalMonths);
    const remainingDays = endDateMoment.diff(
      startDateMoment.clone().add(roundedMonths, 'months'),
      'days',
    );
    return remainingDays > 0
      ? `${roundedMonths * 30 + remainingDays} days`
      : `${roundedMonths} ${roundedMonths === 1 ? 'month' : 'months'}`;
  }
  case 'Per year': {
    const totalMonths = endDateMoment.diff(startDateMoment, 'months', true);
    const roundedYears = Math.floor(totalMonths / 12);
    const remainingDays = endDateMoment.diff(
      startDateMoment.clone().add(roundedYears, 'years'),
      'days',
    );
    return remainingDays > 0
      ? `${roundedYears * 365 + remainingDays} days`
      : `${roundedYears} ${roundedYears === 1 ? 'year' : 'years'}`;
  }
  default:
    return 'Invalid type';
  }
};

export const getRandomDarkColor = () => {
  const r = Math.floor(Math.random() * 128); // Limit red to 0-127
  const g = Math.floor(Math.random() * 128); // Limit green to 0-127
  const b = Math.floor(Math.random() * 128); // Limit blue to 0-127
  return `rgb(${r}, ${g}, ${b})`; // Return the RGB color string
};
export const handleNavigationOnNotification = (screen: string, data: any) => {
  navigationRef.current?.dispatch(
    CommonActions?.reset({
      index: 0, // Index of the screen to navigate to
      routes: [
        {
          name: screen,
          params: data,
        }, // Replace with your screen name
      ],
    }),
  );
};

export const handleClickOnLocalNotification = (screen: string, data: any) => {
  navigationRef.current?.navigate(screen, data);
};

export const resetStack = () => {
  // resetStackScreen('BottomTabsNavigator');;
  // navigationRef?.current?.dispatch(
  //   CommonActions?.reset({
  //     index: 0, // Index of the screen to navigate to
  //     routes: [
  //       {name: 'BottomTabsNavigator'}, // Replace with your screen name
  //     ],
  //   }),
  // );
};

export const openMap = (locationdata?: string) => {
  // Change this to any location you want
  const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
    locationdata,
  )}`;

  Linking.openURL(url).catch(err => console.error('Failed to open map', err));
};

export const formatUSD = (amount: number | string): string => {
  return amount.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
  });
};

export const imagePattern = /\.(jpg|jpeg|png|gif|webp)$/i;

export const isEmailLike = (text: string): boolean => {
  const emailLikeRegex =
    /\b[a-z0-9._%+!'’"#$&*/=?^`{|}~\-]{1,100}[\s\-_*]*(@|\(at\)|\[at\]|a\s*t|at)[\s\-_*]*[a-z0-9\-]{1,100}([\s\-_.]*[\(\[]?dot[\)\]]?|[\s\-_.]*\.)+[\s\-_.]*[a-z]{1,10}\b/gi;

  const domainLikeRegex =
    /\b[a-z0-9\-_.%]{1,50}([\s\-_.]*[\(\[]?dot[\)\]]?|[\s\-_.]*\.)[\s\-_.]*[a-z]{2,10}\b/gi;

  // Return true immediately if raw text contains '@'
  if (text.includes('@')) {
    return true;
  }

  // Normalize by replacing extra symbols with space (like `%%`, `_`, etc.)
  const cleaned = text
    .replace(/[_*%\-\\\/]+/g, '.')
    .replace(/\s+/g, ' ')
    .toLowerCase();

  return emailLikeRegex.test(cleaned) || domainLikeRegex.test(cleaned);
};

export const isPhoneNumberLike = (text: string): boolean => {
  if (!text || typeof text !== 'string') {return false;}
  const cleaned = text.toLowerCase().trim();

  // Avoid time-like formats
  const timeLike = /\b\d{1,2}[:\-]\d{2}\b|\b(am|pm)\b/i;
  if (timeLike.test(cleaned)) {return false;}

  // Avoid common address words
  const addressWords : any[] = [
    // 'road',
    // 'street',
    // 'flat',
    // 'apartment',
    // 'block',
    // 'area',
    // 'sector',
    // 'village',
    // 'building',
    // 'floor',
    // 'room',
    // 'society',
    // 'city',
    // 'state',
    // 'avenue',
    // 'lane',
    // 'cross',
    // 'near',
  ];
  if (addressWords.some((word) => cleaned.includes(word))) {return false;}

  // Avoid PIN/postal codes
  if (/\b\d{6}\b/.test(cleaned)) {return false;}

  // Extract digit sequences
  const digitChunks = cleaned.replace(/[^\d]/g, ' ').split(/\s+/).filter(Boolean);
  const totalDigits = digitChunks.reduce((sum, part) => sum + part.length, 0);

  if (totalDigits < 8 || totalDigits > 14) {return false;}
  if (digitChunks.length > 3) {return false;}

  // Optional obfuscated phone check — ONLY if we suspect phone
  const obfuscated = /(?:\d[\s\W_]?){6,}/;
  if (obfuscated.test(cleaned) && totalDigits >= 8) {return true;}

  return totalDigits >= 8 && digitChunks.length <= 3;
};
export const isSocialLink = (text: string): boolean => {
  const urlRegex = /https?:\/\/[^\s]+/i;
  const socialHandleRegex = /(?:@[\w.]{3,})/i;
  const socialDomainRegex =
    /\b(?:facebook|instagram|linkedin|tiktok|twitter|x\.com|wa\.me|whatsapp|youtube|t\.me|telegram|threads|onlyfans|reddit|tumblr|pinterest|discord|medium|quora|tinder|bumble|signal|wechat|twitch|github|flickr|vimeo|patreon|behance|deviantart|vk|ok\.ru|dribbble|500px|imgur|soundcloud|spotify|bandcamp)(?:\.com|\.org|\.net|\.io|\.me|\.app)?\b/i;

  return (
    urlRegex.test(text) ||
    socialDomainRegex.test(text) ||
    socialHandleRegex.test(text)
  );
};

export const isAddressLike = (text: string): boolean => {
  if (!text || typeof text !== 'string') {return false;}

  const cleaned = text.toLowerCase().trim();

  // ✅ Common address-related keywords
  const addressKeywords = [
    'address',
    // 'street',
    'st',
    'road',
    'rd',
    'lane',
    'ln',
    'avenue',
    'ave',
    'boulevard',
    'blvd',
    // 'drive',
    // 'dr',
    // 'highway',
    // 'hwy',
    'square',
    'sq',
    // 'city',
    // 'town',
    // 'village',
    // 'district',
    // 'state',
    // 'province',
    'zip',
    'zipcode',
    // 'postal',
    // 'postcode',
    // 'pincode',
    // 'apt',
    // 'apartment',
    // 'suite',
    // 'unit',
    // 'building',
    // 'block',
    // 'flat',
    // 'floor',
    // 'area',
    // 'landmark',
    // 'location',
    // 'opposite',
    // 'behind',
  ];

  const addressRegex = new RegExp(`\\b(${addressKeywords.join('|')})\\b`, 'i');

  const zipCodeRegex = /\b\d{5,6}(-\d{4})?\b/; // supports 12345 or 123456 or 12345-6789

  const houseStreetPattern =
    /\b\d{1,5}[\s,]*(?:[a-zA-Z]+\s){0,3}(road|street|lane|avenue|drive|boulevard|blvd|rd|st|ln|dr)\b/;

  const digitCount = (cleaned.match(/\d+/g) || []).length;
  const hasAddressKeyword = addressRegex.test(cleaned);
  const hasZip = zipCodeRegex.test(cleaned);
  const hasHouseStreet = houseStreetPattern.test(cleaned);

  return (
    hasHouseStreet || // e.g., 23 siddhi road
    (hasAddressKeyword && digitCount >= 1) || // keyword + number
    (hasZip && cleaned.length > 10) // zip code + extra info
  );
};

export const getBatchCount = async () => {
  try {
    const resp: any = await getApiData({
      endpoint: BaseSetting.endpoints.batchCount,
      method: 'GET',
    });
    if (resp?.status) {
      const currentCount = store.getState().notification.badgeCount;
      const newCount = resp?.unreadCount || 0;

      if (currentCount !== newCount) {
        store.dispatch(notificationActions.setBadgeCount(newCount));
      }
    }
  } catch (e) {
    console.error('Error fetching notifications:', e);
  }
};




export const getAddressDetails = (): Promise<{
  description: string;
  latitude: number;
  longitude: number;
  shortAddress: string;
}> => {
  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      async position => {
        try {
          const { latitude, longitude } = position.coords;
          const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${BaseSetting.googleApiKey}`,
          );
          const data = await response.json();

          if (data?.results?.length > 0) {
            const address = data.results[0].formatted_address;
            const result = data.results[0];
            const components = result?.address_components;
            console.log('components ===>', data.results[0]);
            

            // Helper to extract component by type
            const getComponent = (types: string[]) =>
              components.find((c: any) => types.some(type => c?.types.includes(type)))?.short_name;

            const locality = getComponent(['locality']);
            const sublocality = getComponent(['sublocality', 'sublocality_level_1']);
            const adminArea = getComponent(['administrative_area_level_1']);
            const country = getComponent(['country']);

            // Compose a fallback short address
            const shortAddress = [locality || sublocality || adminArea, country]
              .filter(Boolean)
              .join(', ') || result.formatted_address;

            resolve({
              description: address,
              latitude,
              longitude,
              shortAddress,
            });
          } else {
            reject(new Error('Unable to fetch address'));
          }
        } catch (err) {
          reject(err);
        }
      },
      error => {
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
      },
    );
  });
};

export const getCurrentLocation = async () => {
  const permission =
    Platform.OS === 'ios'
      ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
      : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

  try {
    const result = await check(permission);

    if (result === RESULTS.GRANTED) {
      const data = await getAddressDetails();
      console.log('Location fetched:', data);
      return data;
    } else if (result === RESULTS.DENIED) {
      const requestResult = await request(permission);
      if (requestResult === RESULTS.GRANTED) {
        return await getAddressDetails();
      } else {
        Toast.show(
          'Permission denied. Location permission is required.',
          Toast.SHORT,
        );
      }
    } else {
      Toast.show(
        'Permission denied. Location permission is required.',
        Toast.SHORT,
      );
    }
  } catch (err) {
    console.error('Error fetching location:', err);
    Toast.show('Error fetching location.', Toast.SHORT);
  }
  return null;
};

export  const handleShareProfile = async (id: string | number, customText?: string) => {
  try {
    await Share.share({
      message: customText || `Hey! Check out this profile: ${BaseSetting.webUrl}user-detail/${id}`,
    });
  } catch (error) {
    console.error('Error sharing link', error);
  }
};

export const isCurrentTimeBetween = (start, end) => {
  if (!start || !end) {return false;}

  const now = dayjs();
  const startTime = dayjs(start);
  const endTime = dayjs(end);

  return now.isAfter(startTime) && now.isBefore(endTime);
};

export const isTimePassed = (start: any) => {
  if (!start) {return false;}

  const now = dayjs();
  const startTime = dayjs(start);

  return now.isAfter(startTime);
};

export const maxFileSize = 10 * 1024 * 1024; // 10MB in bytes


export const containsSocialLink = (text: string): boolean => {
  const lower = text.toLowerCase();
  return [
    'wa.me/',
    't.me/',
    'telegram.me/',
    'instagram.com/',
    'facebook.com/',
    'snapchat.com/',
    'linkedin.com/in/',
    'twitter.com/',
  ].some((social) => lower.includes(social));
};

// Combine all
export const containsSensitiveInfo = (text: string): boolean => {
  return isEmailLike(text) || isPhoneNumberLike(text) || containsSocialLink(text);
};

export const isCombinedMessageSensitive = (messageHistory: string[]): boolean => {
  const merged = messageHistory.slice(-3).join(' ');
  return containsSensitiveInfo(merged);
};

// Function to handle sending a message
export const detectPersonalInfo = (
  message: string,
  isPaid: boolean,
  messages?: { message?: string }[],
): boolean => {
  const normalize = (msg: string) =>
    msg
      .replace(/[\*\-\/\.]/g, ' ')
      .replace(/\s+/g, ' ')
      .toLowerCase()
      .trim();

  const normalizedMessage = normalize(message);

  // Primary checks
  const isPhone = isPhoneNumberLike(normalizedMessage);
  const isSensitive = containsSensitiveInfo(normalizedMessage);
  const isAddress = isAddressLike(normalizedMessage);

  const lastMessage = messages?.[messages.length - 1];

  const lastHadNumber =
    typeof lastMessage?.message === 'string'
      ? isPhoneNumberLike(normalize(lastMessage.message))
      : false;

  const currentHasNumber = isPhone;
  const likelyPhoneAttempt = lastHadNumber && currentHasNumber;

  // 🚫 Unpaid users cannot share contact info at all
  if (!isPaid && (isSensitive || likelyPhoneAttempt || isAddress)) {
    if (messages) {
      Toast.show(translate('chatErrorSocial'), Toast.BOTTOM);
    }
    handleAvailabilityUpdate(
      'isInfoShared',
      true,
      res => {
        console.log('🚀 ~ handleAvailabilityUpdate ~ res:', res);
      },
      err => {
        Toast.show(err?.message || translate('err', ''), Toast.BOTTOM);
      },
    );
    return true;
  }

  // 🧠 Recent message check
  const recentMessages =
    messages
      ?.slice(-3)
      .map(m =>
        typeof m?.message === 'string' ? normalize(m.message) : '',
      ) || [];

  if (
    !isPaid &&
    isSensitive &&
    isCombinedMessageSensitive([...recentMessages, normalizedMessage])
  ) {
    if (messages) {
      Toast.show(translate('chatErrorSocial'), Toast.BOTTOM);
    }
    handleAvailabilityUpdate(
      'isInfoShared',
      true,
      res => {
        console.log('🚀 ~ handleAvailabilityUpdate ~ res:', res);
      },
      err => {
        Toast.show(err?.message || translate('err', ''), Toast.BOTTOM);
      },
    );
    return true;
  }

  return false;
};


export const openDefaultMap = (location: string) => {
  const query = encodeURIComponent(location);
  const url =
    isIOS()
      ? `http://maps.apple.com/?q=${query}`
      : `geo:0,0?q=${query}`; // Opens default maps app on Android

  Linking.openURL(url).catch(err =>
    Toast.show(err?.message || 'Failed to open map', Toast.LONG),
  );
};


export const testIsAddressLike = (): void => {
  const scenarios = [
    // ✅ Positive Cases — Should return TRUE
    '23 MG Road',                               // Street + number
    'Flat 101, Tower 5',                        // Apartment + tower
    'Apt 12, Building 4',                       // Apt + building
    '560001 Bangalore',                         // PIN + city
    '221B Baker Street',                        // Famous address
    '123 Main St, New York, NY',                // Full US address
    '12 Baker Street, London W1U 3BW',          // UK address with postal code
    'H.No 45, Sector 21, Gurgaon',              // House number + sector
    'Plot No 12, Phase 2, Hyderabad',           // Plot + phase
    'Apartment 23, Block B, Whitefield',        // Apartment + block
    'G-12, Green Park Extension, Delhi',        // Complex + area
    '2nd Floor, Tower 6, Prestige Tech Park',   // Floor + tower
    '45 Rue de Rivoli, Paris 75001',            // French address
    'Via Roma 12, 00184 Roma RM, Italy',        // Italian address
    'Apartment 5B, 742 Evergreen Terrace',      // Simpsons' address style

    // ❌ Negative Cases — Should return FALSE
    'Highway 101',                              // Just a highway
    'Near City Mall, 101',                      // Landmark + number
    'Tower 3, Bangalore',                       // Tower only
    'CA 94016',                                 // Zip code only
    '560001',                                   // Pincode only
    'PO Box 123',                               // P.O. Box
    'Landmark: Near Bus Stand',                 // Landmark only
    'Next to Coffee Shop',                      // Vague location
    'First Floor',                              // Generic floor reference
    'Sector 5',                                 // Sector only
    'Behind Big Bazaar',                        // Relative direction only
    'Opposite Metro Station',                   // Opposite landmark

    // 🟡 Edge Cases — Should carefully test boundaries
    '1234567890',                               // Just a 10-digit number
    'A1 Block',                                 // Alphanumeric, no street info
    'MG Road',                                  // Road name only — should depend on logic
    'Whitefield',                               // Area name only — maybe false
    'Building B',                               // Building name only
    'Sector 21, Gurgaon',                       // Missing house number — borderline
    'Block C, Delhi',                           // Block only + city
    '7th Cross',                                // Street name only
    'Flat',                                     // Just word flat — false
    'Tower',                                    // Just tower — false

    // 🌐 International Mixed Edge Cases
    '10 Downing Street, London SW1A 2AA',       // UK PM address — true
    '1600 Amphitheatre Parkway, CA 94043',      // Google HQ — true
    'One Apple Park Way, Cupertino, CA',        // Apple HQ with spelled number — true
    'Piazza del Colosseo, 1, 00184 Roma RM',    // Italian Colosseum address — true
    'Plaza Mayor, Madrid',                      // Plaza only — maybe false
    'Shinjuku-ku, Tokyo 160-0022',              // Japanese address — true

    'Experienced maritime professional holding a 100 Ton Near Shore USCG Endorsement with a broad skill set, including line handling, navigation support, and tender operations. Proficient in rigging and hoisting, with a strong understanding of passage planning. Served as a Yacht Manager, handling various operations, and offering customs and immigration support.',
  ];

  console.log('🔹 Address Detection Test Start 🔹');
  scenarios.forEach((text) => {
    const result = isAddressLike(text);
    console.log(`"${text}" => ${result}`);
  });
  console.log('🔹 Address Detection Test End 🔹');
};
