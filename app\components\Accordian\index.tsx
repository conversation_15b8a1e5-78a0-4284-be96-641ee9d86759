import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { translate } from '@language/Translate';
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  LayoutAnimation,
  Platform,
  UIManager,
  StyleSheet,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

type Props = {
  title: string;
  iconName?: string; // e.g., "account-balance"
  children: React.ReactNode;
};

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const CustomAccordion: React.FC<Props> = ({ title, iconName, children }) => {
  const [expanded, setExpanded] = useState(false);

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(prev => !prev);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.header} onPress={toggleExpand} activeOpacity={0.8}>
        <View style={styles.leftSection}>
          {iconName && <Icon name={iconName} size={20} color={BaseColors.textColor} style={styles.icon} />}
          <View style={styles.briefView}>
            <Text style={[styles.title, {
              marginBottom: 5,
            }]} numberOfLines={2}>{title}</Text>
          </View>
        </View>
        <Icon name={expanded ? 'remove' : 'add'} size={22} color={BaseColors.textColor} />
      </TouchableOpacity>
      {expanded && <View style={styles.content}>{children}</View>}
    </View>
  );
};

export default CustomAccordion;

const styles = StyleSheet.create({
  container: {
    backgroundColor: BaseColors.white,
    borderRadius: 10,
    marginVertical: 8,
    marginTop: 20,
    elevation: 2,
    borderWidth: 1,
    borderColor: BaseColors.borderLightColor,
    paddingHorizontal: 12,
    paddingVertical: 10,
    // flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    paddingRight: 10, // ensure gap between text and plus icon
  },
  icon: {
    marginRight: 10,
    marginTop: 2,
  },
  title: {
    flex: 1,
    flexWrap: 'wrap',
    fontSize: 16,
    // fontWeight: '400',
    color: BaseColors.textColor,
    fontFamily: FontFamily?.OpenSansMedium,
    textTransform: 'capitalize',
  },
  content: {
    // marginTop: 12,
  },
  briefView: {
    marginTop: 5,
  },
  briefText: {
    fontSize: 12,
    fontFamily: FontFamily?.OpenSansRegular,
    color: BaseColors.inputColor,
  },
});
