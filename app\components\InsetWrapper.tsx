import { View } from 'react-native';
import React from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { isIOS } from '@app/utils/CommonFunction';

export default function InsetWrapper({ top = false, children }: any) {
  const insets = useSafeAreaInsets();
  return (
    <View style={{ paddingBottom: isIOS() ? 0 : insets.bottom, flex: 1, paddingTop: top && insets.top }}>
      {children}
    </View>
  );
}
