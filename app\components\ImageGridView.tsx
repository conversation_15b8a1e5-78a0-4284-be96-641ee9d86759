import { View, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import React from 'react';
import { BaseColors } from '@config/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import FastImage from 'react-native-fast-image';

interface Props {
  images: string[];
  openGallery: (index: number) => void;
}


const screenWidth = Dimensions.get('window').width;
const horizontalPadding = 36; // container padding + gaps
const imageSize = (screenWidth - horizontalPadding - 36) / 3; // 3 columns with gaps

export default function ImageGridView({ images, openGallery }: Props) {
  return (
    <View>
      <View style={styles.imageGrid}>
        {images.map((uri: any, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => openGallery(index)}
            activeOpacity={0.85}
            style={styles.imageBox}
          >
            <FastImage source={{ uri: uri?.url || uri, priority: FastImage.priority.high }} style={styles.image} resizeMode="cover" />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}


const styles = StyleSheet.create({
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 5,
    gap: 8,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.12)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageBox: {
    width: imageSize / 1.1,
    height: imageSize / 1.4,
    borderRadius: 6,
    overflow: 'hidden',
    // marginRight: 8,
    marginBottom: 8,
    backgroundColor: BaseColors.secondary,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flexItem: { flex: 1 },

});