// ModalView.tsx
import React, { useEffect, useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
} from 'react-native';
import { BaseColors } from '@config/theme';
import TextInput from '@components/UI/TextInput';
import { getApiData } from '@app/utils/apiHelper';
import Toast from 'react-native-simple-toast';
import Button from '@components/UI/Button';
import { FontFamily } from '@config/typography';
import PaymentBreakdown from '@components/PaymentBreakdown';
import { translate } from '@language/Translate';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { isIOS } from '@app/utils/CommonFunction';
import { endpoints } from '@config/endPoints';
import { isEmpty } from '@app/utils/lodashFactions';
import { useAppSelector } from '@components/UseRedux';

type ModalType = 'noteOnly' | 'payment' | 'counter' | 'approve' | 'cancel';

interface ModalViewProps {
  visible: boolean;
  jobDetail: any;
  initialData?: any;
  type: ModalType;
  amount?: number;
  serviceChargePercent?: number;
  approveLoader?: boolean;
  onCancel: () => void;
  onSend?: () => void;
  onApprove?: () => void;
}

const ApproveCounterModal: React.FC<ModalViewProps> = ({
  visible,
  jobDetail,
  initialData,
  type,
  amount,
  serviceChargePercent,
  approveLoader,
  onCancel,
  onSend,
  onApprove,
}) => {
  const { userData } = useAppSelector((state: any) => state.auth); // Use your RootState type
  const isCounter = type === 'counter';
  const isNoteOnly = type === 'noteOnly';
  const isCancel = type === 'cancel';
  const showBreakdown = type === 'payment' && amount != null;
  const data = jobDetail?.finish_job_request || {};

  const isEmployer = userData?.id === jobDetail?.userId;

  const [note, setNote] = useState(initialData?.note || '');
  console.log('initialData ===>', initialData);

  const [counterAmount, setCounterAmount] = useState('');

  const [noteErr, setNoteErr] = useState(false);
  const [loader, setLoader] = useState(false);

  useEffect(() => {
    if (visible && !isEmpty(initialData)) {
      setNote(initialData?.note || '');
      setCounterAmount(initialData?.amount ? String(initialData?.amount) || '' : '');
    }
  }, [visible]);


  const submitCounter = async () => {
    setLoader(true);
    try {
      const payload = {
        amount: Number(counterAmount),
        note: note,
        job_finish_request_id: jobDetail?.finish_job_request?.job_finish_request_id,
      };
      const res = await getApiData({
        endpoint: endpoints.finishCounter,
        method: 'POST',
        data: payload,
      });
      if (res?.status) {
        onSend?.();
        Toast.show(res?.message, Toast.BOTTOM);
      } else {
        Toast.show(res?.message || translate('error'), Toast.BOTTOM);
      }
      setLoader(false);
    } catch (err) {
      setLoader(false);
      Toast.show(err?.message || translate('error'), Toast.BOTTOM);
    }
  };

  const submitCancel = async () => {
    if (!note || note.trim().length === 0) {
      setNoteErr(true);
      return;
    }
    try {
      setNoteErr(false);
      setLoader(true);
      const payload = {
        reason: note,
        job_finish_request_id: jobDetail?.finish_job_request?.job_finish_request_id,
      };
      const res = await getApiData({
        endpoint: endpoints.cancelFinishCounter,
        method: 'POST',
        data: payload,
      });
      if (res?.status) {
        onSend?.();
        Toast.show(res?.message, Toast.BOTTOM);
      } else {
        Toast.show(res?.message || translate('error'), Toast.BOTTOM);
      }
    } catch (err) {
      Toast.show(err?.message || translate('error'), Toast.BOTTOM);
    } finally {
      setLoader(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onCancel}>
      <View style={styles.overlay}>
        <KeyboardAvoidingView
          style={styles.modalContainer}
          behavior={isIOS() ? 'padding' : 'height'}>
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.modal}>
              <KeyboardAwareScrollView
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ flexGrow: 1 }}>
                <View style={styles.header}>
                  <Text style={styles.title}>
                    {type === 'payment'
                      ? 'Approve'
                      : type === 'counter'
                        ? 'Counter / Request Changes'
                        : 'Request Changes'}
                  </Text>

                  <Text style={styles.subTitle}>
                    {showBreakdown
                      ? translate('submitJob.counterRequestApproveSubTitle')
                      : isCounter
                        ? translate('submitJob.counterRequestSub')
                        : isCancel ? translate('submitJob.requestChangesSubTitle') : translate('submitJob.sureToApprove')}
                  </Text>
                </View>

                {showBreakdown && (
                  <PaymentBreakdown
                    type={isEmployer ? 'employer' : 'seeker'}
                    totalSalary={amount || 0}
                    jobDetails={jobDetail}
                    serviceChargePer={serviceChargePercent}
                    serviceCharge={isEmployer ? data?.service_charge_amount : null}
                    totalWithServiceCharge={data?.total_payable_amount}
                  />
                )}

                {isCounter && (
                  <TextInput
                    title="Amount"
                    placeholderText="Enter amount"
                    keyBoardType="numeric"
                    value={counterAmount || ''}
                    onChange={setCounterAmount || (() => {})}
                  />
                )}

                {(isNoteOnly || isCounter || isCancel) && (
                  <TextInput
                    title="Note / Request"
                    placeholderText="Type here"
                    value={note}
                    mandatory={isCancel}
                    textArea
                    textAreaHeight
                    onChange={setNote}
                    showError={noteErr}
                    errorText={translate('submitJob.noteRequired')}
                  />
                )}

                <View style={styles.buttonRow}>
                  <Button
                    type="outlined"
                    shape="square"
                    onPress={onCancel}
                    style={styles.button}>
                    Cancel
                  </Button>

                  {type === 'payment' ? (
                    <Button onPress={onApprove} loading={approveLoader} style={styles.button}>
                      {translate(isEmployer ? 'yesApprove' : 'Approve')}
                    </Button>
                  ) : (
                    <Button
                      loading={loader}
                      onPress={() => {
                        if (isCancel) {
                          submitCancel();
                        } else {
                          submitCounter();
                        }
                      }} style={styles.button}
                      containerStyle={{ paddingHorizontal:  0 }}>
                      {translate('send')}
                    </Button>
                  )}
                </View>
              </KeyboardAwareScrollView>
            </View>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#00000099',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    width: '100%',
  },
  modal: {
    backgroundColor: BaseColors.white,
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  header: {
    textAlign: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 22,
    color: BaseColors.textBlack,
    fontFamily: FontFamily.OpenSansBold,
    marginBottom: 15,
  },
  subTitle: {
    fontSize: 14,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansMedium,
    marginBottom: 5,
    textAlign: 'center',
  },
  breakdownContainer: {
    marginBottom: 15,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 3,
  },
  label: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansMedium,
    fontWeight: '600',
    marginBottom: 5,
  },
  totalText: {
    fontSize: 16,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.primary,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
    paddingHorizontal:  0,
  },
});

export default ApproveCounterModal;
