/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useMemo, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import Button from '@components/UI/Button';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { translate } from '@language/Translate';
import Toast from 'react-native-simple-toast';
import ExpandableText from '@components/ExpandebleText';
import InfoBanner from '@components/Banner/Banner';
import { endpoints } from '@config/endPoints';
import { getApiData } from '@app/utils/apiHelper';
import ApproveCounterModal from './ApproveCounterModal';
import { initPaymentSheet, presentPaymentSheet } from '@stripe/stripe-react-native';
import ImageGridView from '@components/ImageGridView';
import userActions from '@redux/reducers/userConfig/actions';
import { useAppDispatch, useAppSelector } from '@components/UseRedux';
import { formatDate } from '@app/utils/CommonFunction';

const { setUpdateJobData } = userActions;

export interface FinishRequestProps {
  note?: string;
  media_proof?: string[];
  additionalAmount?: string;
  requested_amount?: string;
  requested_amount_desc?: string;
  job_finish_request_id?: string | any;
  last_request_by?: string;
  status?: string;
  reject_reason?: string;
  requested_amount_status?: string;
  seeker_accept_offer?: boolean | null;
  service_charge_amount?: any;
  total_payable_amount?: any;
  additional_amount_availaible_on?: any;
}

interface Props {
  type: 'seeker' | 'employer';
  data?: FinishRequestProps;
  jobId?: string | number;
  jobDetail?: any;
  handleUpdate?: any;
  viewTimeLine?: Boolean;
  fromChat?: boolean;
}

const screenWidth = Dimensions.get('window').width;
const horizontalPadding = 36; // container padding + gaps
const imageSize = (screenWidth - horizontalPadding - 36) / 3; // 3 columns with gaps

const initData = { visible: false, type: '', data: {} };

const ReviewJobSubmissionCard: React.FC<Props> = ({
  type,
  data,
  jobDetail,
  handleUpdate,
  viewTimeLine,
  fromChat = false,
}) => {
  const dispatch = useAppDispatch();
  const finishRequest = fromChat ? data : jobDetail?.finish_job_request;
  const { updateJobData } = useAppSelector((state: any) => state.userConfig);
  const [paymentInProcess, setPaymentInProcess] = useState(false);
  const navigation = useNavigation<any>();
  const images = data?.media_proof || [];
  const [approveLoader, setApproveLoader] = useState(false);
  const [showApproveCounterModal, setApproveCounterModal] = useState(initData);


  const isEmployer = type === 'employer';
  const isSeeker = type === 'seeker';

  const openGallery = (index: number) => {
    navigation.navigate('GalleryView', {
      type: 'gallery',
      images,
      index,
    });
  };

  useEffect(() => {
    if (updateJobData) {
      handleUpdate('silently');
      setPaymentInProcess(false);
      dispatch(setUpdateJobData(false));
    }
    return () => {};
  }, [updateJobData]);


  const {
    requested_amount: additionalAmount,
    media_proof: imageList,
    note,
    requested_amount_desc,
    last_request_by,
    job_finish_request_id: jobFinishRequestId,
    seeker_accept_offer: seekerAcceptOffer,
    status,
    requested_amount_status,
    additional_amount_availaible_on,
  } = finishRequest || {};

  const isEmployerPaid = useMemo(
    () =>
      requested_amount_status === 'approved' ||
      requested_amount_status === 'processing' ||
      requested_amount_status === 'paid',
    [requested_amount_status],
  );
  console.log('last_request_by ===>', finishRequest, last_request_by);
  

  const lastRequestByEmployer = useMemo(
    () => last_request_by === 'employer' && isEmployer,
    [last_request_by, isEmployer],
  );

  
  
  const seekerAcceptedOffer = finishRequest?.seeker_accept_offer;
  
  const lastRequestBySeeker = useMemo(
    () => (last_request_by === 'seeker' && isSeeker) || (additionalAmount === null && isSeeker),
    [last_request_by, additionalAmount, isSeeker],
  );
  
  const lastRequestByOtherParty = useMemo(
    () =>
      (last_request_by === 'seeker' && isEmployer) ||
    (last_request_by === 'employer' && isSeeker) ||
    (additionalAmount === null && isEmployer),
    [last_request_by, additionalAmount, isEmployer, isSeeker],
  );
  console.log('lastRequestByEmployer ===>', lastRequestByEmployer, isEmployer, last_request_by, lastRequestByEmployer, lastRequestBySeeker, !seekerAcceptedOffer);

  const paidStatus = useMemo(() => isEmployerPaid, [isEmployerPaid]);

  const lastCounterByEmployer = useMemo(
    () =>
      last_request_by === 'employer' &&
      isEmployer &&
      requested_amount_status === 'counter_in_progress',
    [last_request_by, isEmployer, requested_amount_status],
  );

  const lastCounterBySeeker = useMemo(
    () =>
      last_request_by === 'seeker' && isSeeker && requested_amount_status === 'counter_in_progress',
    [last_request_by, isSeeker, requested_amount_status],
  );


  const isEditableEmployer = data?.last_request_by === 'employer' && isEmployer;
  const isREworkRequested = data?.status === 'rework_requested';

  const onCounter = () => {
    setApproveCounterModal({
      visible: true,
      type: 'counter',
      data: {},
    });
  };

  const onReject = () => {
    setApproveCounterModal({
      visible: true,
      type: 'cancel',
      data: {},
    });
  };

  const onEdit = (type?: string) => {
    if (lastCounterByEmployer || lastCounterBySeeker) {
      setApproveCounterModal({
        visible: true,
        type: 'counter',
        data: {
          amount: data?.requested_amount,
          note: requested_amount_desc,
        },
      });
    } else {
      navigation.navigate('SubmitForReview', {
        jobDetail,
        edit: true,
        initialValues: fromChat ? data : jobDetail?.finish_job_request,
      });
    }
    // else if (isREworkRequested) {
    //   setApproveCounterModal({
    //     visible: true,
    //     type: 'cancel',
    //     data: {
    //       note: jobDetail?.finish_job_request?.reject_reason,
    //     },
    //   });
    // } else if (data?.requested_amount_status === 'requested' || data?.requested_amount_status === 'counter_in_progress' || isEditableEmployer) {
    //   setApproveCounterModal({
    //     visible: true,
    //     type: 'counter',
    //     data: {
    //       amount: data?.requested_amount,
    //       note: data?.requested_amount_desc,
    //     },
    //   });
    // }
  };


  const handlePayment = async (params?: any) => {
    try {
      // 👉 Init payment sheet
      const { error: initError } = await initPaymentSheet({
        merchantDisplayName: 'The Harbor App',
        customerId: params?.customer,
        customerEphemeralKeySecret: params?.ephemeralKey,
        paymentIntentClientSecret: params?.paymentIntent,
        allowsDelayedPaymentMethods: true,
        googlePay: {
          merchantCountryCode: 'US',
          testEnv: false,
        },
        applePay: {
          merchantCountryCode: 'US',
        },
        returnURL: 'com.harbor.newapp://stripe-redirect',
      });

      if (initError) {
        console.error('Init error:', initError);
        return;
      }

      // 👉 Present sheet
      const { error: presentError } = await presentPaymentSheet();

      if (presentError) {
        Toast.show(presentError?.message || translate('error'), Toast.BOTTOM);
        setPaymentInProcess(false);
        console.error('Payment failed:', presentError);
      } else {
        handleUpdate('silently');
        setApproveLoader(false);
        console.log('✅ Payment success');
      }
    } catch (err) {
      console.error('Unexpected error:', err);
    } finally {
      setApproveLoader(false);
    }
  };


  const onApproveClick = async () => {
    try {
      setApproveLoader(true);
      const res = await getApiData({
        endpoint: endpoints.approveFinishJobRequest,
        method: 'POST',
        data: {
          job_finish_request_id: data?.job_finish_request_id,
        },
      });
      if (res?.status) {
        setApproveCounterModal(initData);
        if (isEmployer) {
          const payment = res?.data?.payment;
          setTimeout(() => {
            setPaymentInProcess(true);
            handlePayment(payment);
          }, 500);
        }
        Toast.show(res?.message, Toast.BOTTOM);
      } else {
        Toast.show(res?.message || translate('error'), Toast.BOTTOM);
      }
    } catch (err) {
      console.log('err ===>', err);
      Toast.show(err?.message || translate('error'), Toast.BOTTOM);
    } finally {
      setApproveLoader(false);
    }
  };

  const onApprove = () => {
    setApproveCounterModal({
      visible: true,
      type: 'payment',
      data: {},
    });
  };

  // const onApproveClick = async () => {
  //   try {
  //     setApproveLoader(true);
  //     const res = await getApiData({
  //       endpoint: endpoints.finishCounter,
  //       method: 'POST',
  //       data: {
  //         // amount: ,
  //         note: jobDetail?.finish_job_request?.note,
  //         job_finish_request_id: jobDetail?.finish_job_request?.job_finish_request_id,
  //       },
  //     });
  //     if (res?.status) {
  //       Toast.show(res?.message, Toast.BOTTOM);
  //     } else {
  //       Toast.show(res?.message || translate('error'), Toast.BOTTOM);
  //     }
  //   } catch (err) {
  //     console.log('err ===>', err);
  //     Toast.show(err?.message || translate('error'), Toast.BOTTOM);
  //   } finally {
  //     setApproveLoader(false);
  //   }
  // };
  return (
    <>
      {paidStatus ? (
        <InfoBanner
          variant="primary"
          mainText={isEmployer && paidStatus
            ? 'Additional Payment completed'
            : requested_amount_status === 'processing' || paidStatus ? 'Payment Approved' : 'Payment Update Pending'}
          subTitle={requested_amount_status === 'processing' && additional_amount_availaible_on
            ? `You will get your payment on ${formatDate(additional_amount_availaible_on)}`
            : !isEmployer && paidStatus
              ? 'Your payment has been approved, our system will update you soon when your earnings are credited.'
              : ''}
          actionText={fromChat ? null : !viewTimeLine && translate('View All')}
          onActionPress={() => navigation.navigate('ViewTimeline', {
            jobDetail,
          })}
          fromChat={fromChat}
        />
      ) : isREworkRequested ?
        (
          <InfoBanner
            variant="warning"
            mainText={translate(isEmployer ? 'submitJob.waitingForUpdates' : 'submitJob.reworkRequested')}
            subTitle={translate(isEmployer ? 'submitJob.waitingForUpdatesSubTitle' : 'submitJob.reworkRequestedSubTitle')}
            message={data?.reject_reason}
            actionText={isEmployer ? translate('edit') : null}
            onActionPress={() => {
              onEdit?.();
            }}
            subActionText={fromChat ? null : translate('history')}
            onSubActionPress={() => navigation.navigate('ViewTimeline', {
              jobDetail,
            })}
            fromChat={fromChat}
          />
        ) :
        (
          <TouchableOpacity style={styles.card} onPress={() => {
            if (fromChat) {
              navigation.navigate('JobApplicant', {
                jobID: jobDetail?.id,
              });
            }
          }}>
            <View style={styles.header}>
              <Text style={styles.title}>{translate('submitJob.reviewJobSubmission')}</Text>
              {!viewTimeLine && (<TouchableOpacity onPress={() => {
                navigation.navigate('ViewTimeline', {
                  jobDetail,
                });
              }}>
                <Text style={styles.viewAll}>{translate('submitJob.viewAll')}</Text>
              </TouchableOpacity>)}
            </View>

            {additionalAmount ? (
              <Text style={styles.amount}>
                {translate('submitJob.additionalAmountLabel')}{' '}
                <Text style={styles.amountValue}>${additionalAmount}</Text>
              </Text>
            ) : null}

            {/* {data?.note ? (
              <ExpandableText note={data?.note} numberOfLines={3} />
            ) : null} */}
            {data?.note && <View style={{ flexDirection:'row',alignItems:'center', marginTop: 5 }}>
              <Text style={styles.noteTitle}>Job Note: </Text><ExpandableText note={data?.note} numberOfLines={4} />
            </View>}
            {requested_amount_desc && <View style={styles.finishView}>
              <Text style={styles.noteTitle}>Payment Request Note: </Text>
              <ExpandableText note={requested_amount_desc} numberOfLines={4} />
            </View>}

            {/* Images Grid */}
            {images.length > 0 && (
              <ImageGridView images={images} openGallery={openGallery} />
            )}
            {status === 'completed' ? null :
              <>
                {lastRequestByOtherParty ? (
                  <View style={styles.actionRow}>
                    <View style={styles.flexItem}>
                      <Button
                        type="primary"
                        txtStyles={styles.text}style={styles.approveBtn} loading={approveLoader} onPress={() => {
                          onApprove?.();
                        }}>
                        {translate('Approve')}
                      </Button>
                    </View>
                    {additionalAmount &&
                <View style={[styles.flexItem, styles.counterWrap]}>
                  <Button
                    type="outlined"
                    txtStyles={{ ...styles.text, color: BaseColors.primary }}
                    style={styles.counterBtn} onPress={onCounter}>
                    {translate('counter')}
                  </Button>
                </View>}
                    <TouchableOpacity
                      style={styles.rejectBtn} onPress={onReject}>
                      <Ionicons name="close" size={20} color={BaseColors.red} style={styles.rejectBtnText}/>
                    </TouchableOpacity>
                  </View>
                ) : null}
                {paidStatus ? (
                  <Button type="primary" style={styles.approveBtn}>
                      Paid
                  </Button>
                ) : seekerAcceptOffer ?
                  <View style={[styles.footerSingle, styles.flexEnd]}>
                    <Button type="primary"
                      onPress={() => {
                        if (paymentInProcess) {return null;} else { onApprove?.();}
                      }} style={styles.approveBtn}>
                      {translate(paymentInProcess ? 'processing' : 'yesApprove')}
                    </Button>
                  </View>
                  : null}

                {(lastRequestByEmployer || lastRequestBySeeker) && !seekerAcceptedOffer &&
                    !paidStatus && (
                  <Button type="outlined" onPress={onEdit}
                    style={styles.counterBtn}>
                    {translate('edit')}
                  </Button>
                )}
              </>}
          </TouchableOpacity>
        )}
      <ApproveCounterModal
        visible={showApproveCounterModal?.visible}
        jobDetail={jobDetail}
        initialData={showApproveCounterModal?.data}
        type={showApproveCounterModal?.type || 'counter'}
        onCancel={() => setApproveCounterModal(initData)}
        amount={jobDetail?.finish_job_request?.requested_amount}
        onSend={() => {
          setApproveCounterModal(initData);
          handleUpdate('silently');
          // Send action
        }}
        paymentInProcess={paymentInProcess}
        approveLoader={approveLoader}
        onApprove={() => {
          onApproveClick?.();
          // Approve action
        }}
      />
    </>
  );
};

export default ReviewJobSubmissionCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: BaseColors.inputBackground ,
    borderRadius: 10,
    padding: 12,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: BaseColors.borderColor,
    marginTop: 10,
    marginHorizontal: 15,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  title: {
    fontFamily: FontFamily.OpenSansSemiBold,
    fontSize: 15,
    color: BaseColors.textBlack,
  },
  viewAll: {
    fontSize: 14,
    color: BaseColors.primary,
    fontFamily: FontFamily.OpenSansSemiBold,
    textDecorationLine: 'underline',
  },
  flexEnd: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    flexDirection: 'row',
    gap: 10,
  },
  amount: {
    fontSize: 13,
    fontFamily: FontFamily.OpenSansSemiBold,
    // marginBottom: 8,
    color: BaseColors.primary,
  },
  amountValue: {
    fontFamily: FontFamily.OpenSansBold,
  },
  note: {
    fontSize: 13,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    marginBottom: 12,
    marginTop: 8,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 5,
    gap: 8,
  },
  imageBox: {
    width: imageSize / 1.1,
    height: imageSize / 1.4,
    borderRadius: 6,
    overflow: 'hidden',
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: BaseColors.secondary,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.12)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  flexItem: { flex: 1 },
  counterWrap: {
    marginLeft: 8,
    marginRight: 8,
  },
  text: { fontSize: 12 },
  rejectBtnText: { color: BaseColors.red, fontSize: 18 },
  viewBtn: { backgroundColor: BaseColors.inputBackground, paddingHorizontal: 0 },
  approveBtn: { backgroundColor: BaseColors.green, paddingHorizontal: 0 },
  counterBtn: { backgroundColor: BaseColors.inputBackground, paddingHorizontal: 0 },
  rejectBtn: { borderWidth: 1, height: 42, borderRadius: 8, borderColor: BaseColors.red, paddingHorizontal: 0, width: 35, justifyContent: 'center', alignItems: 'center' },
  footerSingle: {
    marginTop: 8,
  },
  noteTitle: { fontFamily: FontFamily.OpenSansBold, fontSize: 12, marginBottom: 4 },
  finishView: { flexDirection:'row',alignItems:'center', marginBottom: 7 },
});
