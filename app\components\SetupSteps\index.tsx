/* eslint-disable react/no-unstable-nested-components */
import React, { useCallback, useEffect, useState } from 'react';
import { SafeAreaView, Text, TouchableOpacity, View } from 'react-native';
import styles from './styles';
import AIcon from 'react-native-vector-icons/AntDesign';
import { BaseColors } from '@config/theme';
import { useSelector } from 'react-redux';
import { translate } from '@language/Translate';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import { useFocusEffect } from '@react-navigation/native';
import AlertModal from '@components/AlertModal';

interface Step {
  number: string;
  title: string;
  active: boolean;
  status?: string; // Optional since not all steps have a status
  verficationComplete?: boolean; // Optional since not all steps have a status
  accessible?: boolean; // Optional since not all steps have a status
  visible?: boolean;
}

const SetupSteps = ({ navigation }: any) => {
  const { userData } = useSelector((state: any) => state.auth);
  const [updateUrl, setUpdateUrl] = useState(false);
  const [modalOpen, setModalOpen] = useState<any>({
    loader: false,
    confirmationModal: false,
  });
  const stepData: Step[] = [
    {
      number: '1',
      title: 'complete',
      active: userData.isProfileSet,
      verficationComplete: userData.isProfileSet,
      accessible: true,
      visible: true,
    },
    {
      number: '2',
      title: 'getVerified',
      active: userData.personaStatus === 'approved' ? true : false,
      status: userData.personaStatus,
      verficationComplete: userData.personaStatus === 'approved' ? true : false,
      accessible: true,
      visible: true,
    },
    {
      number: '3',
      title: 'getYouPaid',
      active: userData.bankAccountVerified === 'verified' ? true : false,
      verficationComplete:
        userData.bankAccountVerified === 'verified' ? true : false,
      accessible: true,
      visible: userData?.isBankVerificationAllow,
    },
    // {
    //   number: '4',
    //   title: 'Learn how to use Harbor',
    //   active: false,
    //   verficationComplete: false,
    // },
  ];
  const [steps, setSteps] = useState<Step[]>(stepData);



  useEffect(() => {
    setSteps(stepData);
    return () => {
    };
  }, [userData]);



  const handleStepPress = (index: number): void => {
    const updatedSteps = steps.map((step, i) => {
      // Update only if the clicked step is not already active
      if (i === index && !step.active) {
        return { ...step, active: true };
      }
      return step; // Leave all other steps unchanged
    });
    setSteps(updatedSteps);
  };

  // async function getWalletData() {
  //   try {
  //     const endpoint = BaseSetting.endpoints.checkKyc;
  //     const response = await getApiData({
  //       endpoint,
  //       method: 'GET',
  //     });
  //     console.log('🚀 ~ getWalletData ~ response:', response);
  //     if (response?.status) {
  //       setUpdateUrl(response?.data);
  //     }
  //   } catch (er) {
  //     console.log('er ==>', er);
  //   }
  // }

  // useFocusEffect(
  //   useCallback(() => {
  //     getWalletData();
  //   }, []),
  // );

  const handleFinishProfilePress = (screen: string) => {
    navigation.navigate(screen); // Navigate to ProfileSetup screen
  };

  const StepItem = ({
    number,
    title,
    active,
    onPress,
    verficationComplete,
    accessible,
  }) => (
    <TouchableOpacity
      style={styles.stepContainer}
      activeOpacity={0.7}
      onPress={onPress}>
      <View
        style={[
          styles.numberContainer,
          !accessible ? { backgroundColor: BaseColors.lightGreyColor }
            : verficationComplete ? styles.activeNumber : styles.inactiveNumber,
        ]}>
        <Text
          style={[
            styles.numberText,
            !accessible  ? { color: BaseColors.textGrey } : verficationComplete
              ? styles.activeNumberText
              : styles.inactiveNumberText,
          ]}>
          {number}
        </Text>
      </View>

      <Text
        style={[
          styles.titleText,
          !accessible && { color: BaseColors.textGrey },
          verficationComplete && { color: BaseColors.primary }, // Change title color when active
        ]}>
        {translate(title)}
      </Text>

      <AIcon
        name="arrowright"
        size={20}
        color={accessible ? active ? BaseColors.primary : BaseColors.titleTextColor : BaseColors.textGrey} // Change icon color when active
      />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.mainView}>
        <Text style={styles.headerText}>{translate('createProfile')}</Text>

        {steps.map((step, index) => {
          if (!step.visible) {
            return null;
          }
          return (
            <StepItem
              key={index}
              accessible={step.accessible}
              number={step.number}
              title={step.title}
              active={step.active}
              verficationComplete={step.verficationComplete}
              onPress={() => {
                if (step?.accessible) {
                  handleStepPress(index);
                  if (index === 0) {
                  // Check if the first step is clicked
                    handleFinishProfilePress('ProfileSetUp'); // Navigate to ProfileSetup
                  }
                  if (index === 1) {
                    handleFinishProfilePress('VerificationDetail');
                  }
                  if (index === 2) {
                    // if (
                    //   userData?.bankAccountVerified === 'verified' ||
                    // userData?.bankAccountVerified === 'notVerified' ||
                    // userData?.bankAccountVerified === 'pending'
                    // ) {
                    //   navigation.navigate('KycCompleteScreen', { 
                    //     uri: updateUrl?.url || '',
                    //   });
                    // } else {
                    handleFinishProfilePress('BankDetails');
                    // }
                  }
                  if (index === 3) {
                    handleFinishProfilePress('VideoTutorial');
                  }
                } else {
                  console.log('Not accessible');
                  setModalOpen(p => ({ ...p, confirmationModal: true }));
                }
              }}
            />
          );})}
      </View>

      <AlertModal
        image
        title={translate('complete', '')}
        visible={modalOpen.confirmationModal}
        setVisible={(val: any) =>
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }))
        }
        lottieViewVisible
        btnYPress={() => {
          navigation.navigate('ProfileSetUp');
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
        }}
        loader={modalOpen?.loader}
        btnYTitle={translate('letsdo')}
        confirmation
        // completeProfile
        titlesty={{ textAlign: 'center' }}
        description={translate('postDescription', '')}
        btnNTitle={translate('maybe')}
        btnNPress={() => {
          setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
        }}
      />
    </SafeAreaView>
  );
};

export default SetupSteps;
