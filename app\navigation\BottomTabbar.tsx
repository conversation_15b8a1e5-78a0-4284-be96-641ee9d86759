import React, { useEffect, useState } from 'react';
import {
  Dimensions,
  Keyboard,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from 'react-native';
import { CustomIcon } from '../config/LoadIcons';
import { translate } from '@language/Translate';
import { FontFamily } from '@config/typography';
import FastImage from 'react-native-fast-image';
import { BaseColors } from '@config/theme';
import { useAppDispatch, useRedux } from '@components/UseRedux';
import { isEmpty } from 'lodash-es';
import SocketActions from '@redux/reducers/socket/actions';
import { useIsFocused } from '@react-navigation/native';
import AlertModal from '@components/AlertModal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import authActions from '@redux/reducers/auth/actions';
import { isIOS } from '@app/utils/CommonFunction';

const { setLoginPopupOpen } = authActions;
const CustomBottomTab = React.memo(({ state, descriptors, navigation }: any) => {
  const { useAppSelector } = useRedux();
  const { languageData } = useAppSelector((state: any) => state.language);
  const { userData, showTour } = useAppSelector((state: any) => state.auth);
  const { totalMsgCount } = useAppSelector((state: any) => state.socket); // Message count from redux
  const { accessToken } = useAppSelector((state: { auth: { accessToken: string } }) => state.auth);


  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true),
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const width = useWindowDimensions().width;
  const insets = useSafeAreaInsets();

  const [sState, setState] = useState(false);
  const [modalOpen, setModalOpen] = useState<any>({
    loader: false,
    confirmationModal: false,
  });

  useEffect(() => {
    setState(true);
  }, [languageData]);

  const focusedOptions = descriptors[state.routes[state.index].key].options;
  if (focusedOptions.tabBarVisible === false) {
    return null;
  }
  const { setTotalMsgCount, emit } =
    SocketActions;

  const TAB_BAR_WIDTH = width - 1;

  const getIconData = (label: any) => {
    switch (label) {
    case 'HomeStackNavigator':
      return {
        iconName: 'search-outlined',
        label: 'searches',
        activeIcon: 'search-filled',
      };
    case 'MessageNavigator':
      return {
        iconName: 'message',
        label: 'Messages',
        activeIcon: 'message-filled',
      };

    case 'StoreStackNavigator':
      return {
        iconName: 'post-outlined',
        label: 'Post',
        activeIcon: 'post-filled',
      };
    case 'ExploreStackNavigator':
      return {
        iconName: 'harbor-outlined',
        label: 'myHarbor',
        activeIcon: 'harbor-filled',
      };
    case 'profilesetUpNavigator':
      return {
        iconName: 'profile-outlined',
        label: 'profile',
        activeIcon: 'frame',
      };
    default:
      return { iconName: 'Home', label: 'home', activeIcon: 'Home' };
    }
  };
  const dispatch = useAppDispatch();
  const [unReadCount, setUnReadCount] = useState<any>({});
  const isFocused = useIsFocused();

  useEffect(() => {
    if (!isEmpty(totalMsgCount)) {
      setUnReadCount(totalMsgCount?.data || totalMsgCount);
      dispatch(
        emit('unread_message_count', { userId: userData?.id }, (res: any) => {
          console.log('unread_message_count resData ===>', res);
          if (res) {
            setTotalMsgCount(res?.totalCount);
            setUnReadCount(res);
            // dispatch(setTotalMsgCount(res));
          }
        }),
      );
    } else {
      dispatch(
        emit('unread_message_count', { userId: userData?.id }, (res: any) => {
          console.log('unread_message_count resData ===>      elsesesese', res);
          if (res) {
            setTotalMsgCount(res?.totalCount);
            setUnReadCount(res);
            // dispatch(setTotalMsgCount(res));
          }
        }),
      );
    }
  }, [totalMsgCount]);

  return (
    <View style={{ marginBottom: isIOS() ? 0 : insets.bottom }}>
      <View style={[styles.tabBarContainer, { width: TAB_BAR_WIDTH, display: isKeyboardVisible ? 'none' : 'flex' }]}>
        {state.routes.map((route: any, index: any) => {
          const { options } = descriptors[route.key];
          const isFocused = state.index === index;
          const { iconName, label, activeIcon } = getIconData(route.name);

          const onPress = () => {
            // if (route.name === 'StoreStackNavigator' && userData?.isProfileSet === false ) {
            //   setModalOpen(p => ({ ...p, confirmationModal: true }));
            // } else {
            if (!accessToken) {
              dispatch(setLoginPopupOpen(true));
            } else {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });
              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name, { merge: true });
              }
            }
            // }
          };

          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };

          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={() => {
                if (!showTour) {
                  onPress();
                }
              }}
              onLongPress={() => {
                if (!showTour) {
                  onLongPress();
                }
              }}
              style={{ flex: 1 }}>
              <View style={[styles.contentContainer]}>
                {route.name === 'profilesetUpNavigator' &&
              userData?.profilePhoto ? (
                    <FastImage
                      source={{
                        uri: userData.profilePhoto,
                        priority: FastImage.priority.high,
                      }}
                      style={styles.image}
                      resizeMode="cover"
                    />
                  ) : (
                    <View
                      style={[
                        route.name === 'profilesetUpNavigator'
                          ? styles.outerCircle
                          : null,
                        {
                          borderColor:
                        route.name === 'profilesetUpNavigator' && !isFocused
                          ? BaseColors.black
                          : BaseColors.primary,
                        },
                      ]}>
                      <CustomIcon
                        name={isFocused ? activeIcon : iconName}
                        color={isFocused ? BaseColors.primary : BaseColors.black}
                        size={
                          route.name === 'profilesetUpNavigator' && !isFocused
                            ? 18
                            : 24
                        }
                        style={styles.icon}
                      />
                    </View>
                  )}

                {/* Message Count Badge */}
                {route.name === 'MessageNavigator' &&
                unReadCount?.totalCount > 0 && (
                  <View style={styles.badgeContainer}>
                    <Text style={styles.badgeText}>
                      {unReadCount?.totalCount}
                    </Text>
                  </View>
                )}

                <Text
                  style={{
                    color: isFocused
                      ? BaseColors.primary
                      : BaseColors.bottomTxtColor,
                    fontFamily: FontFamily.OpenSansMedium,
                    fontSize: languageData === 'en' ? 13 : 11,
                  }}>
                  {translate(label, '')}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}

        <AlertModal
          image
          title={translate('complete', '')}
          visible={modalOpen.confirmationModal}
          setVisible={(val: any) =>
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }))
          }
          lottieViewVisible
          btnYPress={() => {
            navigation.navigate('ProfileSetUp');
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
          }}
          loader={modalOpen?.loader}
          btnYTitle={translate('letsdo')}
          confirmation
          // completeProfile
          titlesty={{ textAlign: 'center' }}
          description={translate('postDescription', '')}
          btnNTitle={translate('maybe')}
          btnNPress={() => {
            setModalOpen((p: any) => ({ ...p, confirmationModal: false }));
          }}
        />
      </View>
    </View>
  );
});

export default CustomBottomTab;

const styles = StyleSheet.create({
  tabBarContainer: {
    flexDirection: 'row',
    position: 'absolute',
    alignSelf: 'center',
    height: Dimensions.get('screen').height / 10,
    bottom: 0,
    backgroundColor: BaseColors.white,
    elevation: 40,
    borderColor: BaseColors.borderColor,
    borderWidth: 1,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  image: {
    width: 28,
    height: 28,
    borderRadius: 16,
    alignSelf: 'center',
    // marginTop: 10,
    alignItems: 'center',
  },
  icon: {
    textAlign: 'center',
    // paddingTop: 18,
  },
  badgeContainer: {
    position: 'absolute',
    top: 10,
    right: 17,
    backgroundColor: BaseColors.primary,
    borderRadius: 8,
    paddingHorizontal: 5,
    paddingVertical: 2,
    minWidth: 18,
    minHeight: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: BaseColors.white,
    fontSize: 11,
    fontFamily: FontFamily.OpenSansMedium,
  },
  outerCircle: {
    width: 30,
    borderRadius: 15,
    height: 30,
    borderColor: BaseColors.primary,
    borderWidth: 1.2,
    justifyContent: 'center',
  },
});
