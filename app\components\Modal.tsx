import React, { useState } from 'react';
import {
  Modal,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { translate } from '@language/Translate';
import { BaseColors } from '@config/theme';
import Button from '@components/UI/Button';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import styles from '@components/BatchComponant/styles';
import { navigationRef } from '@navigation/NavigationService';

const Animated = require('react-native-reanimated').default;
const FadeInDown = require('react-native-reanimated').FadeInDown;

interface ModalComponentProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
}

const ModalComponent: React.FC<ModalComponentProps> = ({
  visible, setVisible,
}) => {

  const type = 'normal';
  const [state, setState] = useState({
    loader: false,
    confirmationModal: false,
  });

  if (visible) {
    return (
      <View style={styles.container}>
        <Modal
          animationType="fade"
          transparent={true}
          visible={visible}
          onRequestClose={() => setVisible(false)}>
          <TouchableWithoutFeedback>
            <View style={styles.modalOverlay}>
              <View style={styles.modalView}>
                <Animated.View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 10,
                  }}
                  entering={FadeInDown}
                  duration={7000}>
                  {type === 'normal' ? (
                    <TouchableOpacity
                      style={[
                        styles.roundView,
                      ]}
                      activeOpacity={1}>
                      <FontAwesome name={'bank'} color={BaseColors.white} size={24} style={{ paddingLeft: 2 }} />
                    </TouchableOpacity>
                  ) : null}

                </Animated.View>
                <Text style={styles.modalHeder}>
                  {translate('connectYourBank')}
                </Text>
                <Text style={styles.modalText}>
                  {translate('makeSureToAddYourBankDetails')}
                </Text>
                <View style={styles.rowBtn}>
                  <Button
                    onPress={() => {
                      setVisible(false);
                      navigationRef.navigate('BankDetails');
                    }}
                    txtStyles={{ fontSize: 14 }}
                    type="primary">
                    {translate('connectBank')}
                  </Button>
                  <Button
                    onPress={() => {
                      setVisible(false);
                    }}
                    txtStyles={{ fontSize: 14 }}
                    type="outlined">
                    {translate('maybe')}
                  </Button>
                </View>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    );
  } return null;
};

export default ModalComponent;
