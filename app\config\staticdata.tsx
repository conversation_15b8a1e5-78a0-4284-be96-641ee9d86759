export const EmployerData = [
  {
    id: 1,
    title: 'Developer',
    rating: '4.6',
    total: '5,546',
    skill1: 'Skill 1',
    skill2: 'Skill 2',
    skill3: 'Skill 3',
    companyName: 'Groovy Techno',
    countryName: 'London, United kingdom',
    Date: 'May 23,2024 - July 23,2024',
    time: '3 weeks ago',
    price: '$200/hr',
  },
];
export const EmployerdetailData = {
  id: 1,
  title: 'Developer',
  rating: '4.6',
  total: '5,546',
  skill1: 'Skill 1',
  skill2: 'Skill 2',
  skill3: 'Skill 3',
  skills: [{ name: 'shipBuilding' }],
  company: 'Groovy Techno',
  location: 'London, United kingdom',
  startDate: 'May 23,2024 - July 23,2024',
  startTime: '3 weeks ago',
  totalSalaryAmount: '$200/hr',
};
export const ApplicantData = {
  id: 1,
  title: 'Developer',
  rating: '4.6',
  reviews: '5,546',
  skill: 'skill 1',

  company: 'Groovy Techno',
  location: 'London, United kingdom',
  Date: 'May 23,2024 - July 23,2024',
  posted: '3 weeks ago',
  salary: '$200/hr',
};

// Define the SmartAlbums type based on the react-native-image-crop-picker library
type SmartAlbums =
  | 'Regular'
  | 'SyncedEvent'
  | 'SyncedFaces'
  | 'SyncedAlbum'
  | 'Imported'
  | 'PhotoStream'
  | 'CloudShared'
  | 'Generic'
  | 'Panoramas'
  | 'Videos'
  | 'Favorites'
  | 'Timelapses'
  | 'AllHidden'
  | 'RecentlyAdded'
  | 'Bursts'
  | 'SlomoVideos'
  | 'UserLibrary'
  | 'Screenshots'
  | 'SelfPortraits'
  | 'DepthEffect'
  | 'LivePhotos'
  | 'Animated'
  | 'LongExposure';

export const smartAlbums: SmartAlbums[] = [
  'Regular',
  'SyncedEvent',
  'SyncedFaces',
  'SyncedAlbum',
  'Imported',
  'PhotoStream',
  'CloudShared',
  'Generic',
  'Panoramas',
  'Videos',
  'Favorites',
  'Timelapses',
  // 'AllHidden',
  'RecentlyAdded',
  // 'Bursts',
  // 'SlomoVideos',
  'UserLibrary',
  'Screenshots',
  'SelfPortraits',
  /** >= iOS 10.2 */
  // 'DepthEffect',
  /** >= iOS 10.3 */
  // 'LivePhotos',
  // 'Animated',
  // 'LongExposure',
];


export const states = [
  { title: 'Alabama' },
  { title: 'Alaska' },
  { title: 'Arizona' },
  { title: 'Arkansas' },
  { title: 'California' },
  { title: 'Colorado' },
  { title: 'Connecticut' },
  { title: 'Delaware' },
  { title: 'Florida' },
  { title: 'Georgia' },
  { title: 'Hawaii' },
  { title: 'Idaho' },
  { title: 'Illinois' },
  { title: 'Indiana' },
  { title: 'Iowa' },
  { title: 'Kansas' },
  { title: 'Kentucky' },
  { title: 'Louisiana' },
  { title: 'Maine' },
  { title: 'Maryland' },
  { title: 'Massachusetts' },
  { title: 'Michigan' },
  { title: 'Minnesota' },
  { title: 'Mississippi' },
  { title: 'Missouri' },
  { title: 'Montana' },
  { title: 'Nebraska' },
  { title: 'Nevada' },
  { title: 'New Hampshire' },
  { title: 'New Jersey' },
  { title: 'New Mexico' },
  { title: 'New York' },
  { title: 'North Carolina' },
  { title: 'North Dakota' },
  { title: 'Ohio' },
  { title: 'Oklahoma' },
  { title: 'Oregon' },
  { title: 'Pennsylvania' },
  { title: 'Rhode Island' },
  { title: 'South Carolina' },
  { title: 'South Dakota' },
  { title: 'Tennessee' },
  { title: 'Texas' },
  { title: 'Utah' },
  { title: 'Vermont' },
  { title: 'Virginia' },
  { title: 'Washington' },
  { title: 'West Virginia' },
  { title: 'Wisconsin' },
  { title: 'Wyoming' },
];
