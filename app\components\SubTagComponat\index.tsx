/* eslint-disable react/no-unstable-nested-components */
import React, { useEffect, useState } from 'react';
import {
  Dimensions,
  FlatList,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Button from '../../components/UI/Button';
import RBSheet from 'react-native-raw-bottom-sheet';
import { BaseColors } from '@config/theme';
import { CustomIcon } from '@config/LoadIcons';
import TextInput from '@components/UI/TextInput';
import { translate } from '@language/Translate';
import { FontFamily } from '@config/typography';
import { getApiData } from '@app/utils/apiHelper';
import BaseSetting from '@config/setting';
import { isArray, isEmpty } from '@app/utils/lodashFactions';
import Toast from 'react-native-simple-toast';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Feather from 'react-native-vector-icons/Feather';
import styles from './styles';
import Entypo from 'react-native-vector-icons/Entypo';
import AIcon from 'react-native-vector-icons/AntDesign';
import { navigationRef } from '@navigation/NavigationService';

const { height } = Dimensions.get('window');

interface SubTagComponant {
  refRBSheet: any;
  setOpenBottomSheet: any;
  title: string;
  openLanguage: boolean;
  Description: string;
  cancelProp: any;
  onClick: any;
  setSelectedLanguage: any;
  selectedLanguage: any;
  doneProp: any;
  showDescription: boolean;
  deleteSty: boolean;
  showCancelbutton: boolean;
  options: any;
  setCheckedItems: any;
  checkedItems: any;
  selectedTags: any;
  setSelectedTags: any;
  setCustomValueList: any;
  customValueList: any;
  Review: any;
  error: any;
  errorText: any;
  type?: string;
}
export default function SubTagComponant({
  refRBSheet,
  setOpenBottomSheet,
  title,
  Description,
  cancelProp,
  onClick,
  doneProp,
  showDescription,
  deleteSty,
  showCancelbutton,
  options,
  selectedTags,
  setSelectedTags,
  setCustomValueList,
  customValueList,
  Review,
  errorText,
  error,
  type,
}: SubTagComponant) {

  const [query, setQuery] = useState('');
  const [loader, setLoader] = useState(false);
  const [noTags, setNoTags] = useState(false);
  const [allOptions, setAllOptions] = useState<any>(options);
  const [suggestionsList, setSuggestions] = useState([]);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [expanded, setExpanded] = useState(false); // State to toggle See More/See Less
  const [tempSelectedTags, setTempSelectedTags] = useState<any>([]); // Temporary state for selected tags
  const [tempCheckedItems, setTempCheckedItems] = useState<{
    [title: string]: {[id: string]: boolean};
  }>({});

  const IOS = Platform.OS === 'ios';

  // Initialize tempCheckedItems and tempSelectedTags when sheet opens
  useEffect(() => {
    // When component mounts or selectedTags changes, initialize the temporary states
    const newCheckedItems: {[title: string]: {[id: string]: boolean}} = {};

    // Deep clone the selectedTags to avoid reference issues
    setTempSelectedTags([
      ...selectedTags.map((tag: any) => ({
        id: tag?.id?.toString(), // Ensure all IDs are strings for consistency
        name: tag.name,
      })),
    ]);

    // Initialize temp checked items based on selectedTags
    if (allOptions) {
      allOptions.forEach((section: any) => {
        newCheckedItems[section.title] = {};
        section.options.forEach((option: any) => {
          const isSelected = selectedTags.some(
            (tag: any) => tag?.id?.toString() === option?.id?.toString(),
          );
          if (isSelected) {
            newCheckedItems[section.title][option.id.toString()] = true;
          }
        });
      });

      setTempCheckedItems(newCheckedItems);
    }
  }, [selectedTags, allOptions]);

  // Function to handle when sheet opens
  const handleSheetOpen = () => {
    // Reset temporary states to match current selectedTags
    const newCheckedItems: {[title: string]: {[id: string]: boolean}} = {};

    // Initialize temp selected tags from current selectedTags (with string IDs)
    setTempSelectedTags([
      ...selectedTags.map((tag: any) => ({
        id: tag?.id?.toString(),
        name: tag.name,
      })),
    ]);

    // Initialize temp checked items based on selectedTags
    if (allOptions) {
      allOptions.forEach((section: any) => {
        newCheckedItems[section.title] = {};
        section.options.forEach((option: any) => {
          const isSelected = selectedTags.some(
            (tag: any) => tag?.id?.toString() === option.id.toString(),
          );
          if (isSelected) {
            newCheckedItems[section.title][option.id.toString()] = true;
          }
        });
      });

      setTempCheckedItems(newCheckedItems);
    }
  };

  const toggleSection = (title: string) => {
    setExpandedSections(prevState =>
      prevState.includes(title) ? [] : [title],
    );
  };

  const toggleCheckbox = (title: string, id: string) => {
    const stringId = id.toString(); // Ensure ID is always a string

    setTempCheckedItems(prevState => {
      const isChecked = !prevState[title]?.[stringId]; // Determine if the checkbox is being checked or unchecked

      const updatedState = {
        ...prevState,
        [title]: {
          ...prevState[title],
          [stringId]: isChecked, // Update only the clicked checkbox
        },
      };

      // Update tempSelectedTags: Remove the tag if unchecked, else add it
      if (isChecked) {
        // If the checkbox was checked, add the tag to tempSelectedTags
        const section = allOptions.find(
          (section: any) => section.title === title,
        );
        const option = section?.options.find(
          (option: any) => option.id.toString() === stringId,
        );
        if (option) {
          setTempSelectedTags(prevTags => [
            ...prevTags,
            { id: option.id.toString(), name: option.name },
          ]);
        }
      } else {
        // If the checkbox was unchecked, remove the tag from tempSelectedTags
        setTempSelectedTags(prevTags =>
          prevTags.filter((tag: any) => tag?.id?.toString() !== stringId),
        );
      }

      return updatedState; // Return the updated tempCheckedItems state
    });
  };

  const removeTag = (tag: {id: string; name: string}) => {
    const tagId = tag?.id?.toString();

    // Remove from selectedTags
    setSelectedTags((prevTags: any) =>
      prevTags.filter((t: any) => t.id.toString() !== tagId),
    );

    // Also update tempSelectedTags and tempCheckedItems to stay in sync
    setTempSelectedTags((prevTags: any) =>
      prevTags.filter((t: any) => t.id.toString() !== tagId),
    );

    // Remove from tempCheckedItems
    setTempCheckedItems(prevState => {
      const updatedState = { ...prevState };
      Object.keys(updatedState).forEach(section => {
        if (updatedState[section][tagId]) {
          delete updatedState[section][tagId];
        }
      });
      return updatedState;
    });
  };

  const fetchOptions = async (text: string) => {
    if (text.length <= 3) {
      return;
    }

    setLoader(true);

    // Use the type parameter to get the correct suggested-skill list type = profile | job
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.subSkillList,
        method: 'GET',
        data: { query: query, type: type || 'job' },
      });

      if (resp?.data && resp?.status) {
        const formattedOptions = resp.data.map((item: any) => ({
          title: item.name,
          options: item.childSkills.map((skill: any) => ({
            id: skill.id.toString(), // Store all IDs as strings
            name: skill.name,
          })),
        }));

        setAllOptions(formattedOptions);
        setSuggestions(resp?.suggestions);
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
    } catch (error) {
      console.error('Error fetching options:', error);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    fetchOptions('ship');
  }, [query]);

  const renderOptions =
    (title: string) =>
      ({ item }: {item: any}) => {
        const isChecked = tempCheckedItems[title]?.[item.id.toString()] || false;

        return (
          <TouchableOpacity
            style={[
              styles.optionContainer,
              isChecked && styles.selectedOptionContainer,
            ]}
            activeOpacity={0.8}
            onPress={() => toggleCheckbox(title, item.id)}>
            <View style={styles.checkboxContainer}>
              {isChecked ? (
                <View style={styles.checkedBox}>
                  <CustomIcon
                    name="checked"
                    size={16}
                    color={BaseColors.primary}
                  />
                </View>
              ) : (
                <View style={styles.uncheckedBox} />
              )}
            </View>
            <Text
              style={[styles.optionText, isChecked && styles.selectedOptionText]}>
              {item.name}
            </Text>
          </TouchableOpacity>
        );
      };

  const renderTags = () => {
    // Show only the first 3 tags if not expanded
    const visibleTags = expanded ? selectedTags : selectedTags.slice(0, 3);

    return (
      <>
        {visibleTags.map((tag: any) => (
          <View key={tag.id} style={styles.tag}>
            <Text style={styles.tagText}>{tag.name}</Text>
            <TouchableOpacity
              onPress={() => (Review === 'reviewType' ? null : removeTag(tag))}>
              <Icon name="close" size={14} color={'#1d559f'} />
            </TouchableOpacity>
          </View>
        ))}
        {selectedTags.length > 3 && (
          <TouchableOpacity
            onPress={() => setExpanded(!expanded)}
            style={styles.seeMoreButton}>
            <Text style={styles.seeMoreText}>
              {expanded ? translate('seeLess', '') : translate('seeMore', '')}
            </Text>
          </TouchableOpacity>
        )}
      </>
    );
  };

  const handleDone = () => {
    // Remove duplicates based on ID (ensuring IDs are strings for comparison)
    const uniqueTags = tempSelectedTags.reduce((acc: any[], tag: any) => {
      const exists = acc.some(t => t.id.toString() === tag?.id?.toString());
      if (!exists) {
        acc.push({
          id: tag?.id?.toString(),
          name: tag.name,
        });
      }
      return acc;
    }, []);

    // Update the selectedTags state with our deduplicated array
    setSelectedTags(uniqueTags);
    setQuery('');

    // Close the sheet
    refRBSheet.current?.close();

    // Call the onClick handler with the updated tags
    onClick(uniqueTags);
  };

  const handleSuggestionPress = (item: any) => {
    // Convert ID to string for consistent comparison
    const itemId = item.id.toString();

    // Check if the item is already selected
    const alreadySelected = selectedTags.some(
      (tag: any) => tag?.id?.toString() === itemId,
    );

    if (!alreadySelected) {
      // Prepare the new tag with string ID
      const newTag = { id: itemId, name: item.name };

      // Add the tag if not already selected
      const updatedTags = [...selectedTags, newTag];

      // Remove duplicates
      const uniqueTags = updatedTags.reduce((acc: any[], tag: any) => {
        const exists = acc.some(t => t.id.toString() === tag?.id?.toString());
        if (!exists) {
          acc.push({
            id: tag?.id?.toString(),
            name: tag.name,
          });
        }
        return acc;
      }, []);

      setSelectedTags(uniqueTags);

      // Also update temp states to stay in sync
      setTempSelectedTags(uniqueTags);
    } else {
      // Toast.show(translate('alreadySelected', ''), Toast.SHORT);
    }
  };
  return (
    <>
      <TouchableOpacity
        onPress={() => {
          if (Review !== 'reviewType') {
            setNoTags(false);
            handleSheetOpen(); // Initialize temporary states
            refRBSheet.current?.open();
          }
        }}
        activeOpacity={1}
        style={{
          borderWidth: 1,
          borderColor: 'transparent',
          width: '100%',
          backgroundColor: error ? '#FFF2F1' : '#f5faff',
          borderRadius: 10,
          padding: 10,
        }}>
        <View style={styles.tagContainer}>
          {selectedTags && selectedTags.length > 0 ? (
            renderTags()
          ) : (
            <Text style={styles.placeholderText}>
              {translate('addSkill', '')}
            </Text>
          )}
        </View>
        <View
          style={{
            right: 0,
            paddingHorizontal: 10,
            position: 'absolute',
            top: '50%',
            width: '15%',
          }}>
          <CustomIcon name="Search" size={20} color={BaseColors.inputColor} />
        </View>
      </TouchableOpacity>
      {error && errorText && <Text style={styles.errorText}>{errorText}</Text>}

      <View
        style={styles.suggestionContainer}>
        <Text
          style={styles.suggestionsText1}>
          {translate('Suggestions', '')} :
        </Text>
        {suggestionsList &&
        !isEmpty(suggestionsList) &&
        isArray(suggestionsList)
          ? suggestionsList.map((item: any, index: number) => (
            <TouchableOpacity
              disabled={Review === 'reviewType' ? true : false}
              key={index}
              style={styles.suggestionList}
              onPress={() => handleSuggestionPress(item)}>
              <Text style={styles.suggestionsText}>{item?.name}</Text>
              <AIcon name="plus" size={14} color={BaseColors.primary} />
            </TouchableOpacity>
          ))
          : null}

        <TouchableOpacity
          disabled={Review === 'reviewType' ? true : false}
          style={[styles.suggestionListPlus]}
          onPress={() => {
            setNoTags(false);
            handleSheetOpen(); // Initialize temporary states
            refRBSheet.current?.open();
          }}>
          <AIcon name="plus" size={14} color={BaseColors.primary} />
        </TouchableOpacity>
      </View>

      {customValueList &&
        !isEmpty(customValueList) &&
        isArray(customValueList)
        ? <View
          style={styles.suggestionContainer}>
          <Text
            style={styles.suggestionsText1}>
            {translate('requestedTags', '')} :
          </Text>
          {customValueList.map((item: any, index: number) => (
            <TouchableOpacity
              disabled={Review === 'reviewType' ? true : false}
              key={index}
              style={[styles.suggestionList, styles.primaryBackground]}
              onPress={() => {
                setCustomValueList(customValueList.filter((i: any) => i !== item));
              }}
            >
              <Entypo name="cross" size={14} color={BaseColors.white} />
              <Text style={[styles.suggestionsText, { color: BaseColors.white }]}>{item?.name}</Text>
            </TouchableOpacity>
          ))}
          {/* <TouchableOpacity
            disabled={Review === 'reviewType' ? true : false}
            style={[styles.suggestionListPlus]}
            onPress={() => {
              setNoTags(true);
              handleSheetOpen(); // Initialize temporary states
              refRBSheet.current?.open();
            }}>
            <AIcon name="plus" size={14} color={BaseColors.primary} />
          </TouchableOpacity> */}

        </View> : null}

      <RBSheet
        ref={refRBSheet}
        height={IOS ? 400 : height * 0.7}
        useNativeDriver={false}
        closeOnDragDown
        onClose={() => {
          setQuery('');
          setNoTags(false);
          setOpenBottomSheet(false);
        }}
        closeOnPressMask
        customStyles={
          deleteSty ? styles.rbSheetCustomDltStyles : styles.rbSheetCustomStyles
        }>
        <View style={styles.languageContainer}>
          <Text style={styles.languageTitle}>{title}</Text>

          {showDescription && (
            <View style={styles.descriptionView}>
              <Text
                style={[
                  styles.descriptionTxtSty,
                  { fontFamily: FontFamily.OpenSansRegular },
                ]}>
                {translate('addTagDescription', '')}
              </Text>
            </View>
          )}
        </View>

        <View style={{ marginHorizontal: 15, marginVertical: 15 }}>
          <TextInput
            searchButton={true}
            value={query}
            placeholderText={translate('searchHere', '')}
            onChange={(value: any) => setQuery(value)}
            tagType={true}
          />
        </View>

        <ScrollView
          style={{
            marginHorizontal: 20,
            maxHeight: IOS
              ? Dimensions.get('screen').height / 2
              : Dimensions.get('screen').height / 1.8,
          }}>
          {(type === 'job' || type === 'user' || type === 'profile') && (noTags || isEmpty(allOptions)) ? (
            <View style={styles.noTagContainer}>
              {/* <Feather name="smile" size={24} color={BaseColors.primary} style={{  marginBottom: 10 }} /> */}

              <Text style={styles.title}>
              ⚓ {translate('requestTag.title')}
              </Text>

              <Text style={styles.subtitle}>
                {/* {translate('requestTag.subtitle')} */}
              </Text>

              <Button
                onPress={() => {
                  setQuery('');
                  setNoTags(false);
                  refRBSheet.current?.close();
                  navigationRef.navigate('RequestTag', {
                    initialTagName: query,
                    tagList: customValueList,
                    type: type,
                    onTagListChange: setCustomValueList,
                  });
                }}
                containerStyle={styles.button}>
                <Text style={styles.buttonText}>
                  {translate('requestTag.button')}
                </Text>
              </Button>
            </View>
          ) : (
            <>
              {allOptions.map((section: any) => (
                <View key={section.title}>
                  <TouchableOpacity
                    onPress={() => toggleSection(section.title)}
                    style={styles.sectionHeader}>
                    <Text style={styles.sectionTitle}>{section.title}</Text>
                    <CustomIcon
                      name="ArrowUp"
                      size={24}
                      color={BaseColors.primary}
                      style={{
                        transform: [
                          {
                            rotate: expandedSections.includes(section.title)
                              ? '0deg'
                              : '180deg',
                          },
                        ],
                      }}
                    />
                  </TouchableOpacity>

                  {expandedSections.includes(section.title) && (
                    <View
                      style={{
                        marginTop: 10,
                      }}>
                      <FlatList
                        data={section.options}
                        renderItem={renderOptions(section.title)}
                        keyExtractor={item => item.id}
                      />
                    </View>
                  )}
                </View>
              ))}
            </>
          )}
        </ScrollView>

        <View style={styles.fixedButtonContainer}>
          <Button onPress={handleDone} type="text" style={{ width: '100%' }}>
            {doneProp}
          </Button>
        </View>
      </RBSheet>
    </>
  );
}
