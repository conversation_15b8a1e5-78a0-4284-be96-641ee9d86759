/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { Platform, Text, View } from 'react-native';

import { translate } from '@language/Translate';
import styles from './styles';
import { formatUSD } from '@app/utils/CommonFunction';

const IOS = Platform.OS === 'ios';

interface PaymentBreakdownProps {
  serviceChargePer: number;
  totalSalary: number; // or string if it's received as text
  serviceCharge: string | number; // or string if it's received as text
  totalWithServiceCharge: number; // or string if it's received as text
  type: string;
  jobDetails: any;
}

const PaymentBreakdown: React.FC<PaymentBreakdownProps> = ({
  serviceChargePer,
  totalSalary,
  serviceCharge,
  totalWithServiceCharge,
  type,
  jobDetails,
}) => {
  const formattedTotal = totalWithServiceCharge || 0;
  const finishRequest = jobDetails?.finish_job_request;
  const additionalPaymentApproved = ['approved', 'processing', 'paid'].includes(
    finishRequest?.requested_amount_status,
  );
  const totalAmount =  type === 'employer'
    ? formattedTotal +
        (additionalPaymentApproved ? finishRequest?.total_payable_amount || 0 : 0)
    : formattedTotal +
        (additionalPaymentApproved ? finishRequest?.requested_amount || 0 : 0);

  return (
    <View style={[styles.jobCostContainer]}>
      <Text style={styles.jobCostText}>
        {type === 'seeker'
          ? translate('payoutBreakDown', '')
          : translate('paymentBreakDown', '')}
      </Text>

      <View style={[styles.rowSpaceBetween, { paddingTop: IOS ? 15 : 10 }]}>
        <Text style={styles.totalSalaryText}>Total Pay</Text>
        <Text style={styles.salaryAmountText}>
          {formatUSD(totalSalary)}
          {/* ${totalSalary || '0'} */}
        </Text>
      </View>
      {serviceChargePer > 0 || Number(serviceCharge) > 0 ? (
        <View style={[styles.rowSpaceBetween, { paddingTop: IOS ? 10 : 5 }]}>
          <Text style={styles.serviceChargeText}>
            {translate('serviceFee', '')}{' '}
            {/* {Number(serviceChargePer) > 0 ? `(${serviceChargePer}%)`
             : null} */}
          </Text>
          <Text style={styles.salaryAmountText}>{formatUSD(Number(serviceCharge))}
            {/* ${serviceCharge || '0'} */}
          </Text>
        </View>
      ) : null}
      {additionalPaymentApproved ? (
        <>
          <View style={styles.bottomBorder} />
          <View style={[styles.rowSpaceBetween, { paddingTop: IOS ? 10 : 5 }]}>
            <Text style={styles.serviceChargeText}>
              {translate('additionalPayment', '')}{' '}
            </Text>
            <Text style={styles.salaryAmountText}>{formatUSD(Number(finishRequest?.requested_amount || 0))}
            </Text>
          </View>
          {type === 'employer' && (<View style={[styles.rowSpaceBetween, { paddingTop: IOS ? 10 : 5 }]}>
            <Text style={styles.serviceChargeText}>
              {translate('additionalPaymentFee', '')}{' '}
            </Text>
            <Text style={styles.salaryAmountText}>{formatUSD(Number(finishRequest?.service_charge_amount || 0))}
            </Text>
          </View>)}
        </>
      ) : null}
      {/* {type === 'employer' ? ( */}
      <>
        <View style={styles.bottomBorder} />
        <View
          style={[styles.rowSpaceBetween, { paddingVertical: IOS ? 14 : 7 }]}>
          <Text style={styles.estimatedChargesText}>
            {type === 'seeker' ? 'Total Pay' : 'Total Estimated Charges'}
          </Text>
          <Text style={styles.totalsalaryAmountText}>
            {formatUSD(Number(totalAmount || 0))}
            {/* ${formattedTotal || '0'} */}
          </Text>
        </View>
      </>
      {/* ) : null} */}
    </View>
  );
};

export default PaymentBreakdown;
