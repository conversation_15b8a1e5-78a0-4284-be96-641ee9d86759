/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Modal,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import styles from './styles';
import BUTTON from '@components/UI/Button';

import FastImage from 'react-native-fast-image';
import { CustomIcon } from '@config/LoadIcons';
import { BaseColors, BaseStyles } from '@config/theme';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { isEmpty } from '@app/utils/lodashFactions';
import { translate } from '@language/Translate';
import { FontFamily } from '@config/typography';
import { Images } from '@config/images';
import { getBadgeImage } from '@app/utils/CommonFunction';

const { width } = Dimensions.get('window');

interface SeekerCardProps {
  item: any;
  onSave?: any;
  saveLoader?: any;
  type?: string;
  onActionClick?: any;
  applicantsLoader?: any;
  showApplicant?: boolean;
  navigation?: any;
  list?: any;
  applicantType?: any;
  Id?: any;
  jobtype?: any;
  bodyType?: any;
  buttons?: boolean;
}

const SeekerCard: React.FC<SeekerCardProps> = ({
  item,
  onSave,
  saveLoader = false,
  type = '',
  onActionClick = () => {},
  applicantsLoader = false,
  showApplicant = false,
  navigation,
  list,
  Id,
  applicantType,
  jobtype,
  bodyType,
  buttons = true,
}) => {
  const [imageError, setImageError] = useState(false);
  const [showSkillsModal, setShowSkillsModal] = useState<boolean>(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [numberOfLines, setNumberOfLines] = useState(0);

  const USER_VERIFIED = item?.personaStatus === 'approved' ? true : false;

  // Helper function to capitalize the first letter of a string
  const capitalizeFirstLetter = (str: any) => {
    if (!str) {
      return '';
    }
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  };
  // Get skills based on applicant type
  const getSkills = () => {
    if (applicantType === 'applicantType') {
      return item?.approvedApplicant?.skills || [];
    }
    return item?.skills || [];
  };

  const getDescriptionText = () => {
    if (applicantType === 'applicantType') {
      return item?.approvedApplicant?.about || '--------';
    }
    return item?.approvedApplicant?.about || item?.about || '-';
  };

  const description = getDescriptionText();
  // Render skills modal
  const SkillsModal = () => (
    <Modal
      visible={showSkillsModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowSkillsModal(false)}>
      <TouchableOpacity
        style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        activeOpacity={1}
        onPress={() => setShowSkillsModal(false)}>
        <View
          style={{
            backgroundColor: 'white',
            padding: 20,
            borderRadius: 10,
            width: '80%',
            maxHeight: '80%',
          }}>
          <Text
            style={{
              fontSize: 18,
              fontWeight: 'bold',
              marginBottom: 15,
              color: BaseColors.black,
            }}>
            {translate('tags')}
          </Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
            {getSkills().map((skill: any, index: number) => (
              <View key={`${index}+1`} style={styles.skillSty}>
                <Text style={styles.skillTxtSty}>{skill?.name || ''}</Text>
              </View>
            ))}
          </View>
          <TouchableOpacity
            style={{
              marginTop: 20,
              alignSelf: 'flex-end',
            }}
            onPress={() => setShowSkillsModal(false)}>
            <Text style={{ color: BaseColors.primary, fontSize: 16 }}>Close</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  // Get initials from the name
  const getInitials = (name: any) => {
    if (!name) {
      return '';
    }
    const nameParts = name.split(' ');
    const initials = (nameParts[0]?.[0] || '') + (nameParts[1]?.[0] || '');
    return initials.toUpperCase();
  };
  const firstName = item?.approvedApplicant?.firstName || item?.firstName;
  const currentBadge =
    item?.approvedApplicant?.currentBadgeName || item?.currentBadgeName;

  const profilePhoto = item?.approvedApplicant?.profilePhoto
    ? item?.approvedApplicant?.profilePhoto
    : item?.profilePhoto;
  const initials = getInitials(`${firstName}`);

  const avgRate =
    applicantType === 'applicantType'
      ? item?.approvedApplicant?.averageRate
      : item?.averageRate;
  const avgCount =
    applicantType === 'applicantType'
      ? item?.approvedApplicant?.ratingCount
      : item?.ratingCount;

  const name =  item?.firstName || item?.lastName
    ? `${capitalizeFirstLetter(
      item?.firstName,
    )} ${capitalizeFirstLetter(item?.lastName?.charAt(0))}.`
    : item?.approvedApplicant?.firstName ||
                      item?.approvedApplicant?.lastName
      ? `${capitalizeFirstLetter(
        item?.approvedApplicant?.firstName,
      )} ${capitalizeFirstLetter(
        item?.approvedApplicant?.lastName?.charAt(0),
      )}.`
      : '';

  return (
    <TouchableOpacity
      activeOpacity={1}
      disabled={jobtype === 'jobDetail' ? true : false}
      onPress={() => {
        if (showApplicant) {
          navigation.navigate('ApplicantDetails', {
            applicantData: item,
            job: list,
            Id: Id,
            reviewType: 'reviewbyEmployer',
            userId: item?.id,
            seekerProfile: 'seekerProfile',
          });
        } else {
          navigation.navigate('ApplicantDetails', {
            applicantDetail: 'applicantDetail',
            applicantData: item,
            job: list,
            Id: Id,
            type: type,
            reviewType: 'reviewbyEmployer',
            seekerProfile: 'seekerProfile',
            userId:
              applicantType === 'applicantType'
                ? item?.approvedApplicant?.userId
                : item?.id,
          });
        }
      }}
      style={[
        styles.cardBorder,
        {
          width:
            bodyType === 'home'
              ? Dimensions.get('screen').width * 0.95
              : '100%',
          marginHorizontal: bodyType === 'home' ? 7 : 0,
        },
      ]}>
      <View style={styles.innerPadding}>
        <View style={styles.rowDirection}>
          <View style={styles.imgView}>
            {!imageError && profilePhoto ? (
              <FastImage
                source={
                  profilePhoto
                    ? { uri: profilePhoto }
                    : item?.gender === 'female'
                      ? Images.female
                      : Images.user
                }
                style={{
                  width: '100%',
                  height: '100%',
                  borderRadius: width * 0.6,
                }}
                resizeMode="cover"
                onError={() => setImageError(true)} // Set error state on image load failure
              />
            ) : (
              <FastImage
                source={item?.gender === 'female' ? Images.female : Images.user}
                style={{
                  width: '100%',
                  height: '100%',
                  borderRadius: width * 0.6,
                }}
                resizeMode="cover"
                onError={() => setImageError(true)} // Set error state on image load failure
              />
            )}
            {USER_VERIFIED ? (
              <View
                style={{
                  height: 20,
                  width: 20,
                  position: 'absolute',
                  bottom: -2,
                  right: -2,
                }}>
                <FastImage
                  source={Images.verified}
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: width * 0.0125, // Assuming 5 is about 1.25% of the width
                  }}
                  resizeMode={FastImage.resizeMode.cover} // Optional: Set resize mode if needed
                  onError={() => setImageError(true)} // Set error state on image load failure
                />
              </View>
            ) : null}
          </View>
          <View style={styles.txtViewSty}>
            <View style={styles.row}>
              {/* // Inside your component render */}
              <View
                style={{
                  flex: 1,
                  // width:
                  //   bodyType === 'home'
                  //     ? Dimensions.get('screen').width / 3
                  //     : '100%',
                  flexDirection: 'row',
                  alignContent: 'center',
                }}>
                <Text numberOfLines={2}
                  ellipsizeMode="tail"
                  adjustsFontSizeToFit={false} style={[styles.titleTxtSty, { fontSize: name?.length > 20 ? 14 : 16 }]}>
                  {name}
                  {item?.isAvailable ? (
                    <View
                      style={[ BaseStyles.onlineIndicator,
                        { marginTop: 4, marginLeft: 5 },
                      ]}
                    />
                  ) : null}
                </Text>

              </View>
            </View>
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                // paddingTop: 1,
              }}>
              <CustomIcon
                name="jobLocation"
                size={14}
                color={BaseColors.primary}
              />

              <Text
                numberOfLines={1}
                style={[
                  styles.desTxtSty,
                  {
                    width:
                      bodyType === 'home'
                        ? Dimensions.get('screen').width / 3.3
                        : Dimensions.get('screen').width / 3,
                  },
                ]}>
                {item?.approvedApplicant?.location
                  ? item?.approvedApplicant?.location
                  : item?.shortAddress ? item?.shortAddress
                    : item?.location || ''}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.bookmarkContainer}>
            <View style={styles.imageView}>
              <FastImage
                source={getBadgeImage(currentBadge)}
                style={styles.medalIcon}
                resizeMode="contain"
              />
            </View>
            {item?.pro && (
              <View style={[styles.levelView, { marginHorizontal: 5 }]}>
                <Text style={styles.proTxtSty}>Pro</Text>
              </View>
            )}
            <TouchableOpacity
              // style={styles.bookmarkContainer}
              onPress={() => {
                onSave(item);
              }}>
              {saveLoader ===
              (applicantType === 'applicantType'
                ? item?.approvedApplicant?.userId
                : item?.id) ? (
                  <ActivityIndicator color={BaseColors.primary} />
                ) : (
                  <Ionicons
                    name={
                      (applicantType === 'applicantType' &&
                      item?.approvedApplicant?.savedUser) ||
                    (applicantType !== 'applicantType' &&
                      (item?.isSaved || !isEmpty(item?.savedUser)))
                        ? 'bookmark-sharp' // Filled bookmark if saved
                        : 'bookmark-outline' // Outline bookmark if unsaved
                    }
                    size={25}
                    color={
                      item?.isSaved || !isEmpty(item?.savedUser)
                        ? BaseColors.primary // Primary color if saved
                        : BaseColors.black // Black color if unsaved
                    }
                  />
                // <CustomIcon
                //   name="bookmark"
                //   size={25}
                //   color={item?.isSaved ? BaseColors.primary : BaseColors.red}
                // />
                )}
            </TouchableOpacity>
            {/* <CustomIcon name="bookmark" size={25} color={BaseColors.textGrey} /> */}
          </View>
        </View>

        <View style={styles.underlineViewSty} />
        <View style={styles.bodyDataSty}>
          <Text
            numberOfLines={showFullDescription ? undefined : 2}
            style={[
              styles.decriptionTXtSty,
              showFullDescription && { marginBottom: 8 },
            ]}
            onTextLayout={e => {
              // Get number of lines from the text layout event
              setNumberOfLines(e.nativeEvent.lines.length);
            }}>
            {capitalizeFirstLetter(description)}
          </Text>
          {numberOfLines >= 2 && (
            <TouchableOpacity
              onPress={() => setShowFullDescription(!showFullDescription)}
              style={{ marginTop: 4 }}>
              <Text
                style={{
                  color: BaseColors.primary,
                  fontSize: 14,
                  fontFamily: FontFamily.OpenSansRegular,
                  textTransform: 'capitalize',
                }}>
                {showFullDescription ? 'Show Less' : 'Show More'}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.underlineViewSty} />

        <View style={styles.skillsRateView}>
          <View style={styles.bottomViewSty}>
            <View style={styles.skillsViewSty}>
              {getSkills()
                .slice(0, 2)
                .map((skill: any, index: number) => (
                  <View
                    key={`${index}+1`}
                    style={[styles.skillSty, { marginRight: 8 }]}>
                    <Text style={styles.skillTxtSty}>{skill?.name || ''}</Text>
                  </View>
                ))}
              {getSkills().length > 2 && (
                <TouchableOpacity
                  onPress={() => setShowSkillsModal(true)}
                  style={[styles.skillSty, { marginRight: 8 }]}>
                  <Text style={styles.skillTxtSty}>
                    +{getSkills().length - 2} more
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
          {/* {avgRate ? ( */}
          <View style={styles.reviewViewSty}>
            <CustomIcon name="fillStar" color={BaseColors.starColor} />
            <Text style={styles.ratingTxtSty}>
              {Number(avgRate).toFixed(1) || '0'}
            </Text>
            {/* {avgCount ?  */}
            <Text>({avgCount || '0'})</Text>
            {/* : null} */}
          </View>
          {/* ) : null} */}
        </View>
        {/* Render the Skills Modal */}
        <SkillsModal />
        {type === 'applicants' && item?.userJob?.status !== 'Declined' ? (
          <View style={styles.applicationsBtns}>
            <BUTTON
              onPress={() => {
                onActionClick(item, 'Approved');
              }}
              loading={
                applicantsLoader?.type === 'Approved' &&
                item?.id === applicantsLoader?.id
              }
              style={styles.approveBtn}
              txtStyles={{ fontSize: 14 }}
              type="text">
              {translate('Approve', '')}
            </BUTTON>

            <BUTTON
              onPress={() => {
                onActionClick(item, 'Declined');
              }}
              loading={
                applicantsLoader?.type === 'Declined' &&
                item?.id === applicantsLoader?.id
              }
              style={styles.declineBtn}
              txtStyles={{ color: BaseColors.btnRed, fontSize: 14 }}
              type="outlined">
              {translate('Decline', '')}
            </BUTTON>
          </View>
        ) : null}
        {type === 'applicants' && item?.userJob?.status === 'Declined' ? (
          <View style={styles.applicationswholeBtn}>
            <BUTTON
              style={{
                ...styles.declineBtn,
                width: '100%',
                backgroundColor: '#f8bdbd',
              }}
              txtStyles={{ color: BaseColors.btnRed, fontSize: 14 }}
              type="outlined">
              {translate('Declined', '')}
            </BUTTON>
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  );
};

export default SeekerCard;
