import {Dimensions, Platform, StyleSheet} from 'react-native';
import {FontFamily} from '@config/typography';
import {BaseColors} from '@config/theme';

const {width, height} = Dimensions.get('window');

const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  innerContainer: {
    paddingHorizontal: 5,
  },
  mainImgView: {
    alignSelf: 'center',
    // paddingTop: height * 0.16,
  },
  imgView: {
    alignItems: 'center',
    justifyContent: 'center',
    width: Dimensions.get('screen').width / 2.5,
    height: Dimensions.get('screen').height / 11,
  },

  titleContainer: {
    paddingTop: height * 0.01,
  },
  mainTitle: {
    textAlign: 'center',
    fontSize: width * 0.08,
    color: BaseColors.logoColor,
    fontFamily: FontFamily.OpenSansSemiBold,
  },
  welcomeText: {
    textAlign: 'center',
    fontSize: width * 0.055,
    color: BaseColors.textColor,
    paddingTop: height * 0.012,
    fontFamily: FontFamily.OpenSansRegular,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: width * 0.04,
    color: BaseColors.textColor,
    paddingTop: height * 0.012,
    fontFamily: FontFamily.OpenSansRegular,
  },
  inputContainer: {
    marginTop: height * 0.056,
  },
  noErrorContainer: {
    borderColor: BaseColors.white,
  },
  inputTitle: {
    color: BaseColors.textBlack,
    fontFamily: FontFamily.OpenSansBold,
    fontSize: 20,
  },
  textInput: {
    color: BaseColors.inputColor,
  },
  codeText: {
    color: BaseColors.inputColor,
  },
  textInputContainer: {
    borderColor: BaseColors.primary,
    borderWidth: width * 0.003,
    borderRadius: width * 0.02,
  },
  verifyCodeText: {
    fontSize: width * 0.035,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
    paddingTop: IOS ? 8 : 0,
    paddingHorizontal: 4,
    // textTransform: 'capitalize',
  },
  buttonContainer: {
    marginTop: 50,
    // marginHorizontal: width * 0.08,
  },
  buttonStyle: {
    backgroundColor: BaseColors.primary,
  },
  toggleContainer: {
    paddingTop: height * 0.012,
    marginHorizontal: width * 0.09,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    flex: 1,
    flexWrap: 'wrap',
  },
  toggleText: {
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: width * 0.038,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,

    // flex: 1,
  },
  termsText: {
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 11,
    color: BaseColors.inputColor,
    fontFamily: FontFamily.OpenSansRegular,
    // textTransform: 'capitalize',
    // flex: 1,
  },
  toggleActionText: {
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: width * 0.017,
    fontSize: width * 0.038,
    color: BaseColors.primary,
    textDecorationLine: 'underline',
    fontFamily: FontFamily.OpenSansRegular,
    // textTransform: 'capitalize',
    // flex: 1,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: height * 0.04,
  },
  divider: {
    width: '30%',
    borderWidth: 0.6,
    height: 0,
    borderColor: '#BDBDBD',
  },
  orText: {
    paddingHorizontal: width * 0.04,
    fontSize: width * 0.04,
    fontFamily: FontFamily.OpenSansRegular,
  },
  appleButton: {
    marginVertical: height * 0.025,
    // marginHorizontal: width * 0.08,
  },
  googleButton: {
    borderColor: '#333333',
    borderWidth: 0.6,
    borderRadius: 0,
    alignSelf: 'center',
  },
  continueText: {
    textAlign: 'center',
    color: '#000000',
    fontFamily: '',
    fontWeight: '600',
    fontSize: 16,
  },
  svgView: {
    position: 'absolute',
    top: -20,
  },
  buttonWrapper: {
    borderWidth: 1, // Border for customization
    borderColor: BaseColors.primary, // Custom border color
    borderRadius: 10, // Optional: matches button style
    overflow: 'hidden',
  },
  devIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 8,
    height: 8,
    backgroundColor: '#4caf50',
    borderRadius: 4,
  },
});
