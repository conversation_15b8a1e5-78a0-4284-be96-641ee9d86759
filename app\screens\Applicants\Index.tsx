/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  RefreshControl,
  ScrollView,
  View,
} from 'react-native';
import styles from './styles';
import Header from '@components/Header';
import { BaseColors } from '@config/theme';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import NoRecord from '@components/NoRecord';
 import Toast from 'react-native-simple-toast';
import { translate } from '@language/Translate';
import { useFocusEffect } from '@react-navigation/native';
import AlertModal from '@components/AlertModal';
import CustomOfferModal from '@components/CustomOfferModal';
import moment from 'moment';
import { useSelector } from 'react-redux';
import CounterOfferCard from '@components/SeekerCard/CounterOfferCard';

export default function Applicants({ navigation, route }: { navigation: any, route: any }) {
  const { params } = route;
  const job = params?.job || {};
  const { accessToken } = useSelector((state: any) => state.auth);
  const { userData } = useSelector((s: any) => s.auth);
  const [list, setList] = useState({
    data: [],
    pagination: { currentPage: 1, isMore: null },
  });

  const [jobDetail, setJobDetail] = useState<any>({});
  const [state, setState] = useState({
    bottomLoading: false,
    refreshing: false,
    loader: false,
    saveLoader: false,
    applicantsLoader: false,
    confirmationModal: false,
    applicant: {},
    job: {},
    type: '',

    //
    customOfferModal: false,
    customOfferLoader: false,
    counterOfferModal: false,
  });

  const { loader, refreshing, bottomLoading, customOfferModal, counterOfferModal } = state;

  const getDetails = useCallback(async () => {
    try {
      console.log('job');

      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.jobDetail + `/${job?.id}` + `${accessToken ? '' : '/details'}`,
        method: 'GET',
      });

      if (resp?.data && resp?.status) {
        setJobDetail(resp?.data); // Update list with new data
        setState((p: any) => ({
          ...p,
          loader: false,
          refreshing: false,
          bottomLoading: false,
        }));
      }
    } catch (error) {
      console.error('Error fetching list:', error);
      // Toast.show('Failed to fetch data.', Toast.SHORT);
    }
  },
  [job],
  );
  // Function to get list of seekers or employers
  const getList = async (page = 1, bottomLoader: boolean = false) => {
    if (loader) {return;} // Prevent duplicate calls
    if (bottomLoader) {
      setState((p: any) => ({ ...p, bottomLoading: true }));
    } else {
      setState((p: any) => ({ ...p, loader: true }));
    }
    const data: any = { page, limit: 5, jobId: job?.id };
    try {
      const resp = await getApiData({
        endpoint: BaseSetting.endpoints.listApplicants,
        method: 'GET',
        data: data,
      });
      console.log('resp ==>', JSON.stringify(resp));
      if (resp?.data && resp?.status) {
        getDetails();
        setList(p => ({
          ...p,
          data:
            page > 1
              ? [...list?.data, ...resp?.data?.items]
              : resp.data?.items || [],
          pagination: resp?.data?.pagination,
        }));
      } else {
        Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
      }
    } catch (e) {
      setState((p: any) => ({
        ...p,
        loader: false,
        refreshing: false,
        bottomLoading: false,
      }));
      console.log('ERRR', e);
    }
  };

  const handleClick = useCallback(async (item: any, type: string) => {
    if (item?.counterOfferId) {
      if (type === 'pay') {
        navigation.navigate('JobDetailScreen', { applicant: item, type, job: jobDetail });
        return false;
      }
      setState((p: any) => ({
        ...p,
        counterOfferModal: true,
        applicant: item,
        type: type,
        job: jobDetail || job,
      }));
    } else if (type === 'Approved') {
      navigation.navigate('JobDetailScreen', { applicant: item, type, job: jobDetail });
    } else {
      console.log('called ---->', item);
      setState((p: any) => ({
        ...p,
        confirmationModal: true,
        applicant: item,
        type,
        job,
      }));
    }
  }, [jobDetail]);

  const updateJobStatus = useCallback(
    async (type: string) => {
      setState((p: any) => ({ ...p, applicantsLoader: true }));
      try {
        const resp = await getApiData({
          endpoint: BaseSetting.endpoints.updateuserJob,
          method: 'POST',
          data: { jobId: job?.id, status: type, userId: state?.applicant?.id },
        });
        if (resp?.status) {
          setState((p: any) => ({
            ...p,
            confirmationModal: false,
            applicant: {},
            job: {},
          }));
          // Toast.show(
          //   resp?.message ||
          //   translate(
          //     type === 'Approved' ? 'successApplied' : 'declinedApplied',
          //     '',
          //   ),
          //   Toast.SHORT,
          // );
          getList(1, bottomLoading);
        } else {
          Toast.show(resp?.message || translate('err', ''), Toast.SHORT);
        }
        setState((p: any) => ({
          ...p,
          applicantsLoader: false,
        }));
      } catch (e) {
        setState((p: any) => ({
          ...p,
          applicantsLoader: false,
        }));
        console.log('ERRR', e);
      }
    },
    [state],
  );

  const onRefresh = React.useCallback(() => {
    if (!loader) {
      setState((p: any) => ({ ...p, refreshing: true }));
      getList(1, bottomLoading);
      setState((p: any) => ({ ...p, refreshing: false }));
    }
  }, [loader]);

  const ListEndLoader = () => {
    if (!loader && bottomLoading) {
      return <ActivityIndicator color={BaseColors.primary} size={'small'} />;
    }
  };

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    if (
      isCloseToBottom &&
      list?.pagination?.isMore &&
      !loader &&
      !bottomLoading
    ) {
      getList(Number(list?.pagination?.currentPage) + 1, true);
    }
  };

  const renderItem = (item: any) => {
    return (
      <CounterOfferCard
        item={item}
        type={'applicants'}
        onActionClick={handleClick}
        applicantsLoader={false}
        navigation={navigation}
        jobDetail={job}
        // buttons
        buttons={jobDetail?.approvedApplicant?.status !== 'Approved'}
      />
    );
  };
  const isEmployer = userData?.id === jobDetail?.userId;


  const onSubmit = useCallback(
    async (values: any, type?: string) => {
      const isEdit = state?.type === 'edit';
      if (state?.applicant?.counterOfferId || type === 'custom') {
        if (state?.type === 'Approved' || state?.type === 'Declined') {
          const url = BaseSetting.endpoints.updateCounterOfferEdit;
          const apiData = {
            counterOfferId: state?.applicant?.counterOfferId || null,
            userId: userData?.id,
            jobId: jobDetail?.id || null,
            userType: state?.applicant?.offerBy === 'seeker' ? 'employer' : 'seeker',
            status: String(state?.type).toLowerCase(),
          };
          try {
            setState((p: any) => ({ ...p, customOfferLoader: true }));
            const response = await getApiData({
              endpoint: url,
              data: apiData,
              method:  'POST',
            });

            if (response?.status) {
              setState({ ...state, customOfferModal: false, confirmationModal: false, counterOfferModal: false, customOfferLoader: false, type: '', applicant: {}, job: {} });
              getList();
              // Toast.show(response?.message || translate('err'), Toast.SHORT);
            } else {
              // Toast.show(response?.message || translate('err'), Toast.SHORT);
            }
          } catch (error) {
            console.error('Error submitting form:', error);
          }


        } else {
          setState((p) => ({ ...p, customOfferLoader: true }));
          console.log('🚀 ~ handleSubmit values:', values);
          // duration must of following :perHour,perDay,perWeek,perMonth"
          const payload: any = {
            jobId: jobDetail?.id,
            finalPrice: values?.salaryAmount,
            duration: 'perHour',
            startTime: values?.startTime ? moment(values.startTime).format('hh:mm A') : '',
            endTime: values?.endTime ? moment(values.endTime).format('hh:mm A') : '',
            startDate: values?.startDate ? moment(values.startDate).format('MM/DD/YYYY') : '',
            endDate: values?.endDate ? moment(values.endDate).format('MM/DD/YYYY') : '',
            message: values?.msg || '',
            sendBy: state?.type === 'counter' ? isEmployer ? userData?.id :  state?.applicant?.receiveBy : userData?.id,
            receiveBy: state?.type === 'counter' ? isEmployer ? state?.applicant?.id :  state?.applicant?.sendBy : isEmployer ? state?.applicant?.id : jobDetail?.userId,
            // sendBy: isEmployer ? userData?.id : state?.applicant?.receiveBy,
            // receiveBy: isEmployer ? state?.applicant?.id :  state?.applicant?.sendBy,
            offerBy: userData?.id == jobDetail?.userId ? 'employer' : 'seeker',
          };
          console.log('payload ---->', payload);
          if (isEdit) {
            payload.counterOfferId = state?.applicant?.counterOfferId || null;
            payload.userId = userData?.id;
            payload.userType = userData?.id == jobDetail?.userId ? 'employer' : 'seeker';
          }

          const endPoint = isEdit ? BaseSetting.endpoints.createCounterOfferEdit : BaseSetting.endpoints.createOffer;
          try {
            const response = await getApiData({
              endpoint: endPoint,
              data: payload,
              method:  'POST',
            });

            console.log('response ==>', response);

            if (response?.status) {
              setState({ ...state, customOfferModal: false, counterOfferModal: false, customOfferLoader: false, applicant: false, job: {} });
              getList();
              // Toast.show(response?.message, Toast.SHORT);
            } else {
              setState({ ...state, customOfferLoader: false });
              // Toast.show(response?.message || translate('error'), Toast.SHORT);
            }
          } catch (error) {
            setState({ ...state, customOfferLoader: false });
            console.error('Error submitting form:', error);
          }
        }
        return false;
      } else if (type === 'Approved') {
        navigation.navigate('JobDetailScreen', { applicant: state?.applicant, type, job });
      } else {
        setState((p: any) => ({
          ...p,
          confirmationModal: true,
          applicant: state?.applicant,
          type,
          job,
        }));
      }
    },
    [customOfferModal, state,  counterOfferModal],
  ); // Only depend on jobId to avoid unnecessary re-creations

  useFocusEffect(
    React.useCallback(() => {
      console.log('useFocuss effect called');
      getList(1, false);
      return () => { };
    }, []),
  );

  const handleOnClose = useCallback(() => {
    setState({ ...state,
      customOfferModal: false,
      counterOfferModal: false,
      applicant: {},
      type: '',
      job: {},
    });
  }, [state]);

  return (
    <View style={[styles.container]}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon="back-arrow"
        title={translate('Applicants', '')}
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <ScrollView
        onScroll={handleScroll}
        // scrollEventThrottle={0.5}
        style={{
          ...styles.mainView,
          // marginBottom: Dimensions.get('screen').height / 150,
        }}
        nestedScrollEnabled={true}
        contentContainerStyle={{ flexGrow: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.primary]} // Customize refresh indicator color
            tintColor={BaseColors.primary} // Customize refresh indicator color (Android)
          />
        }
        showsVerticalScrollIndicator={false}>
        {loader ? (
          <View style={styles.centerMain}>
            <ActivityIndicator color={BaseColors.primary} />
          </View>
        ) : (
          <FlatList
            data={list?.data || []}
            keyExtractor={(item: any) => `${item.id}+1`}
            renderItem={({ item }) => renderItem(item)}
            contentContainerStyle={{
              //   marginTop: 30,
              marginBottom: Dimensions.get('screen').height / 9,
            }}
            ListEmptyComponent={
              <View style={styles.centerMain}>
                <NoRecord
                  title={'noApplicants'}
                  type="employer"
                  iconName="employer"
                />
              </View>
            }
            style={{
              ...styles.mainView,
            }}
            scrollEnabled={false} // Disable FlatList's scrolling
            ListFooterComponent={ListEndLoader} // Loader when loading next page.
          />
        )}
      </ScrollView>



      <CustomOfferModal
        setState={setState}
        state={state}
        title={translate('customOffer')}
        jobDetail={job}
        onSubmit={(values: any) => {
          onSubmit(values);
        }}
        navigation={navigation}
        onCancel={() => { handleOnClose(); }}
      />
      {state.confirmationModal && (
        <AlertModal
          image
          title={translate('declineJob', '')}
          visible={state.confirmationModal}
          setVisible={(val: any) =>
            setState((p: any) => ({ ...p, confirmationModal: false }))
          }
          btnYPress={() => {
            updateJobStatus('Declined');
          }}
          loader={state?.applicantsLoader}
          btnYTitle={translate('YES')}
          btnNTitle={translate('CANCEL')}
          btnNPress={() => {
            setState((p: any) => ({ ...p, confirmationModal: false }));
          }}
          confirmation
        />
      )}
    </View>
  );
}
