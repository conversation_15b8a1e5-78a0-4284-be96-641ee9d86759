import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backBtn: {
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    paddingHorizontal: 16,
  },
  tagSection: {
    backgroundColor: '#F4FAFE',
    borderRadius: 8,
    padding: 12,
    marginTop: 16,
  },
  tagTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  tagTitle: {
    fontSize: 14,
    color: BaseColors.textBlack,
    marginRight: 6,
    fontFamily: FontFamily.OpenSansBold,
  },
  tagList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 10,
  },
  infoTooltip: {
    fontSize: 12,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansRegular,
    marginBottom: 8,
  },
  tag: {
    backgroundColor: BaseColors.primary,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginHorizontal: 2,
  },
  tagText: {
    color: BaseColors.white,
    fontSize: 13,
    fontFamily: FontFamily.OpenSansRegular,
  },
  infoText: {
    fontSize: 14,
    marginTop: 16,
    fontFamily: FontFamily.OpenSansRegular,
  },
  input: {
    borderRadius: 6,
    marginTop: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    gap: 10,
  },
  cancelBtn: {
    width: '48%',
  },
  cancelText: {
    color: '#555',
  },
});
