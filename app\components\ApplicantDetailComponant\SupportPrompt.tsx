import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { BaseColors } from '@config/theme';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/AntDesign';

const SupportPrompt: React.FC = () => {
    const navigation = useNavigation<NavigationProp<any>>(); // You can replace `any` with your actual navigation type

    const handleClick = () => {
      navigation.navigate('ContactUs'); // Assumes a 'ContactUs' screen is registered
    };

  return (
    <View style={styles.container}>
      <Pressable
        style={({ pressed }) => [
          styles.button,
          pressed && styles.buttonPressed
        ]}
        onPress={handleClick}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel="Need help? Contact Us"
      >
        <Icon name="questioncircleo" size={16} color={BaseColors.primary} style={styles.icon} />
        <Text style={styles.text}>Need help? Contact Us</Text>
      </Pressable>
    </View>
  );
};

export default SupportPrompt;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 20,
  },
  button: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: BaseColors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 18,
    borderRadius: 12,
  },
  buttonPressed: {
    backgroundColor:'transparent',
    transform: [{ scale: 0.98 }],
  },
  icon: {
    marginRight: 8,
  },
  text: {
    color: BaseColors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
});
