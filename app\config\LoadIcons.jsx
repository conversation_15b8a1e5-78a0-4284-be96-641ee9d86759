import React from 'react';
// import FORTAB from '../components/MQ';
import { PixelRatio, Platform } from 'react-native';
import { createIconSetFromIcoMoon, IconProps } from 'react-native-vector-icons';
import icoMoonConfig from './selection.json';

const CustomIcon: React.ComponentType<IconProps> = createIconSetFromIcoMoon(
  icoMoonConfig
);
const iconSize = 22;
const navIconSize =
  __DEV__ === false && Platform.OS === 'android'
    ? PixelRatio.getPixelSizeForLayoutSize(8)
    : iconSize;
const replaceSuffixPattern = /--(active|big|small|very-big)/g;

const CustIcon = {
  ElogPreference: [navIconSize, '#7E87AE'],
};

const iconsArray = [[CustIcon, CustomIcon]];

const iconsMap = {
  'Profile-converted': [navIconSize, '#7E87AE'],
  'reward-filled': [navIconSize, '#7E87AE'],
  Home: [navIconSize, '#7E87AE'],
  Archive: [navIconSize, '#7E87AE'],
  true: [navIconSize, '#7E87AE'],
  notification: [navIconSize, '#7E87AE'],
  Tag: [navIconSize, '#7E87AE'],
  save: [navIconSize, '#7E87AE'],
  translate: [navIconSize, '#7E87AE'],
  Sort: [navIconSize, '#7E87AE'],
  search: [navIconSize, '#7E87AE'],
  Rewards: [navIconSize, '#7E87AE'],
  logo: [navIconSize, '#7E87AE'],
  export: [navIconSize, '#7E87AE'],
  close: [navIconSize, '#7E87AE'],
  down: [navIconSize, '#7E87AE'],
  Calander: [navIconSize, '#7E87AE'],
  briefcase: [navIconSize, '#7E87AE'],
  back: [navIconSize, '#7E87AE'],
  appleicon: [navIconSize, '#7E87AE'],
  'sqaure-plus': [navIconSize, '#7E87AE'],
  message: [navIconSize, '#7E87AE'],
  MyHarbor: [navIconSize, '#7E87AE'],
  reload: [navIconSize, '#7E87AE'],
  star: [navIconSize, '#7E87AE'],
  warning: [navIconSize, '#7E87AE'],
  messages: [navIconSize, '#7E87AE'],
  clock: [navIconSize, '#7E87AE'],
  profile: [navIconSize, '#7E87AE'],
  setting: [navIconSize, '#7E87AE'],
  add: [navIconSize, '#7E87AE'],
  document: [navIconSize, '#7E87AE'],
  gps: [navIconSize, '#7E87AE'],
  medalstar: [navIconSize, '#7E87AE'],
  messagenotif: [navIconSize, '#7E87AE'],
  briefcase1: [navIconSize, '#7E87AE'],
  frame: [navIconSize, '#7E87AE'],
  'medal-star1': [navIconSize, '#7E87AE'],
  add1: [navIconSize, '#7E87AE'],
  ArrowUp: [navIconSize, '#7E87AE'],
  ChevronLeft: [navIconSize, '#7E87AE'],
  Plain: [navIconSize, '#7E87AE'],
  'bi-imagealt': [navIconSize, '#7E87AE'],
  ButtonMd: [navIconSize, '#7E87AE'],
  flag: [navIconSize, '#7E87AE'],
  'Ellipse-35-Stroke': [navIconSize, '#7E87AE'],
  home: [navIconSize, '#7E87AE'],
  payoutInfo: [navIconSize, '#7E87AE'],
  payout: [navIconSize, '#7E87AE'],
  HelpCenter: [navIconSize, '#7E87AE'],
  checked: [navIconSize, '#7E87AE'],
  language: [navIconSize, '#7E87AE'],
  Location: [navIconSize, '#7E87AE'],
  trash: [navIconSize, '#7E87AE'],
  Logout: [navIconSize, '#7E87AE'],
  PrivacyPolicy: [navIconSize, '#7E87AE'],
  Privacy: [navIconSize, '#7E87AE'],
  Search: [navIconSize, '#7E87AE'],
  'Shop-Bag': [navIconSize, '#7E87AE'],
  Terms: [navIconSize, '#7E87AE'],
  Upload: [navIconSize, '#7E87AE'],
  'Vector-Stroke': [navIconSize, '#7E87AE'],
  apple: [navIconSize, '#7E87AE'],
  VectorStroke: [navIconSize, '#7E87AE'],
  Vector: [navIconSize, '#7E87AE'],
  AboutApp: [navIconSize, '#7E87AE'],
  mainLogo: [navIconSize, '#7E87AE'],
  Job: [navIconSize, '#7E87AE'],
  Jobfill: [navIconSize, '#7E87AE'],
  Seeker: [navIconSize, '#7E87AE'],
  Seekerfill: [navIconSize, '#7E87AE'],
  bookmark: [navIconSize, '#7E87AE'],
  fillStar: [navIconSize, '#7E87AE'],
  jobLocation: [navIconSize, '#7E87AE'],
  'Group-1171274822-1': [navIconSize, '#7E87AE'],
  DESC: [navIconSize, '#7E87AE'],
};
const iconsLoaded = new Promise(resolve => {
  const allFonts = [iconsArray].map(iconArrayMain =>
    Promise.all(
      iconArrayMain.map(iconArray =>
        Promise.all(
          Object.keys(iconArray[0]).map(iconName =>
            // IconName--suffix--other-suffix is just the mapping name in iconsMap
            iconArray[1].getImageSource(
              iconName.replace(replaceSuffixPattern, ''),
              iconArray[0][iconName][0],
              iconArray[0][iconName][1],
            ),
          ),
        ).then(
          sources =>
            Object.keys(iconArray[0]).forEach(
              (iconName, idx) => (iconsMap[iconName] = sources[idx]),
            ),
          // resolve(true);
        ),
      ),
    ).then(() => {
      resolve(true);
    }),
  );
  return Promise.all(allFonts);
});

export { iconsMap, iconsLoaded, CustomIcon };
