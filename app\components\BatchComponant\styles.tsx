import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    // flex: 1,
    // position: 'relative', // Make sure the container has relative position
  },
  confettiContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1, // Make sure confetti is behind the modal
  },
  medalIcon: {
    width: 80,
    height: 80,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Dim the background
    justifyContent: 'center',
    alignItems: 'center',
    // zIndex: 1, // Modal should be above confetti
  },
  modalView: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    zIndex: 3, // Modal content above overlay
    width: '80%',
    // flex: 1,
  },
  modalHeder: {
    fontSize: 18,
    fontFamily: FontFamily.OpenSansBold,
    textAlign: 'center',
    color: "#262626",
    marginBottom: 5,
  },
  modalTitleText: {
    fontSize: 18,
    fontFamily: FontFamily.OpenSansBold,
    textAlign: 'center',
    color: BaseColors.textColor,
  },
  modalText: {
    fontSize: 16,
    textAlign: 'center',
    color:BaseColors.textColor
  },
  loader: {
    height: 100,
    width: '100%',
    // flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 200,
  },
  btnView: {
    marginTop: 20,
    // alignSelf: 'center',
  },

  floatingButton: {
    width: 50,
    height: 50,
    borderRadius: 50 / 2,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },

  roundView: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BaseColors.primary,
  },

  rowBtn: {
    marginTop: 20,
    // height: 90,
    gap: 5,
  },

});
