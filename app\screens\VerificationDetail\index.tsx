import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import Header from '@components/Header';
import { translate } from '@language/Translate';
import { useSelector } from 'react-redux';
import { Images } from '@config/images';
import Button from '@components/UI/Button';
import { BaseColors } from '@config/theme';
import { Environment, Inquiry } from 'react-native-persona';
import AlertModal from '@components/AlertModal';
import { getSystemSettings } from '@app/utils/CommonFunction';
import InsetWrapper from '@components/InsetWrapper';
const LottieView = require('lottie-react-native').default;
const VerificationDetail = ({ navigation }: {navigation: any}): any => {
  const [state, setState] = useState({
    loader: false,
    confirmationModal: false,
    payoutReciept: false,
  });
  const { userData } = useSelector((state: any) => state.auth);
  const [active, setActive] = useState<Boolean>(false);
  const [totalpersonacount, setTotalPersonacount] = useState<string>('');

  const [totalCurrentcount, setTotalCurrentcount] = useState<string>('40');

  const [status, setStatus] = useState(userData.personaStatus); // pending, inprogress, approved, declined userData.personaStatus
  useEffect(() => {
    setStatus(userData.personaStatus);
    return () => {};
  }, [userData.personaStatus]);

  const startVerification = () => {
    Inquiry.fromTemplate('itmpl_EcM8NJYa18dDvb91tTNY19QiJycf')
      .environment(Environment.PRODUCTION)
      .referenceId(userData.personaReferenceId)
      .onComplete((inquiryId, status, fields) =>
        console.log(
          'Complete Inquiry',
          `Inquiry ${inquiryId} completed with status "${status}."`,
        ),
      )
      .onCanceled((inquiryId, sessionToken) =>
        console.log('Canceled Inquiry', `Inquiry ${inquiryId} was cancelled`),
      )
      .onError(error => console.log('Error Inquiry', error.message))
      .build()
      .start();
  };

  const getStatusContent = (s = 'approved'): any => {
    console.log('🚀 ~ getStatusContent ~ s:', s);
    switch (s) {
    case 'pending':
      return {
        title: 'Start your document verification',
        description: 'The process takes approximately 5 minutes to complete.',
        color: BaseColors.primary,
        lottie: Images.pending,
      };
    case 'inprogress':
      return {
        title: 'Documents Under Review',
        description:
            'Our team is currently reviewing your submitted documents.',
        color: '#FABD05',
        lottie: Images.waiting,
      };
    case 'approved':
      return {
        title: 'Verification Successful',
        description:
            'Congratulations! Your verification process is complete.',
        color: '#208661',
        lottie: Images.approved,
      };
    case 'declined':
      return {
        title: 'Verification Failed',
        description:
            'Please try again or contact support for further assistance.',
        color: '#FF0000',
        lottie: Images.declined,
      };
    default:
      return null;
    }
  };

  const openverificationModal = () => {
    if (!active) {
      setState(p => ({ ...p, confirmationModal: true }));
    } else if (Number(totalCurrentcount) >= Number(totalpersonacount)) {
      setState(p => ({ ...p, confirmationModal: true }));
    } else {
      startVerification();
    }
  };

  useEffect(() => {
    const fetchSettings = async () => {
      const resp = await getSystemSettings();
      console.log('🚀 ~ fetchSettings ~ resp:', resp);

      // Check the value of persona_verification_status
      const personaVerificationStatus = resp?.data?.find(
        setting => setting.slug === 'persona_verification_status',
      );
      // Check and set totalPersonacount from persona_verification_max_count
      const personaVerificationMaxCount = resp?.data?.find(
        setting => setting.slug === 'persona_verification_max_count',
      );

      const personaVerificationCurrentCount = resp?.data?.find(
        setting => setting.slug === 'persona_verified_total_count',
      );
      // If the value of persona_verification_status is 1, set active to true, otherwise set to false
      if (personaVerificationStatus) {
        setActive(personaVerificationStatus.value === 1);
      }
      if (personaVerificationMaxCount) {
        setTotalPersonacount(personaVerificationMaxCount.value.toString());
      }
      if (personaVerificationCurrentCount) {
        setTotalCurrentcount(personaVerificationCurrentCount.value.toString());
      }
    };

    fetchSettings();
  }, []);
  return (
    <InsetWrapper>
      <View
        style={{
          ...styles.container,
        }}>
        <Header
          leftIcon="back-arrow"
          onLeftPress={() => {
            navigation.goBack();
          }}
          title={translate('', '')}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={[styles.container]}>
            <Text style={styles.title}>
              {translate('secureDocumentVerification', '')}
            </Text>
            <Text style={styles.subtitle}>
              {translate('personaDescription', '')}
            </Text>
            <View style={styles.info}>
              <Text style={styles.infoTitle}>
                {translate('secureAndTrusted', '')}
              </Text>
              <Text style={styles.infoText} numberOfLines={2}>
                {translate('secureTxt', '')}
              </Text>
            </View>
            <View style={styles.info}>
              <Text style={styles.infoTitle}>
                {translate('quickConvenient', '')}
              </Text>
              <Text style={styles.infoText}>
                {translate('completeYourVierification', '')}
              </Text>
            </View>
            <View style={styles.mainView}>
              <LottieView
                autoSize={true}
                source={getStatusContent(status).lottie}
                autoPlay={true}
                style={styles.loader}
              />
            </View>
            <View style={styles.status}>
              <Text
                style={[
                  styles.statusTitle,
                  {
                    color: getStatusContent(status).color,
                  },
                ]}>
                {getStatusContent(status).title}
              </Text>
              <Text style={styles.statusDescription}>
                {getStatusContent(status).description}
              </Text>
            </View>
            <View
              style={{
                justifyContent: 'flex-end',
                alignContent: 'flex-end',
                flex: 1,
              }}>
              {userData?.isProfileSet === true &&
                (status === 'declined' || status === 'pending') && (
                <Button
                  type="text"
                  style={{ width: '100%', marginVertical: 15 }}
                  onPress={openverificationModal}>
                  {status === 'declined'
                    ? translate('retryVerification', '')
                    : translate('startVerification', '')}
                </Button>
              )}
              {status === 'declined' && (
                <Button
                  type={'outlined'}
                  onPress={() => navigation.navigate('ContactUs', '')}
                  style={{ width: '100%', marginVertical: 15, borderWidth: 0 }}>
                  {translate('contactSupport', '')}
                </Button>
              )}
            </View>
          </View>
        </ScrollView>
        {state.confirmationModal && (
          <AlertModal
            image
            title={translate('modalTitle', '')}
            description={translate('modalDescription', '')}
            visible={state.confirmationModal}
            setVisible={(val: any) =>
              setState((p: any) => ({ ...p, confirmationModal: false }))
            }
            okbtnPromp
            btnOkPress={() => {
              setState((p: any) => ({ ...p, confirmationModal: false }));
            }}
            loader={state?.loader}
            confirmation
            lottieViewVisible
          />
        )}
      </View>
    </InsetWrapper>
  );
};

const styles = StyleSheet.create({
  mainView: {
    height: 250,
    zIndex: 111,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loader: {
    height: 175,
    width: '100%',
    borderRadius: 200,
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textTransform: 'capitalize',
  },
  subtitle: {
    fontSize: 16,
    color: '#555',
    marginBottom: 16,
    textTransform: 'capitalize',

  },
  info: {
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textTransform: 'capitalize',

  },
  infoText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
    textTransform: 'capitalize',

  },
  status: {
    alignItems: 'center',
    justifyContent: 'center',
    alignContent: 'center',
    marginTop: 40,
  },
  statusTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  statusDescription: {
    fontSize: 16,
    color: '#555',
    textAlign: 'center',
    marginBottom: 16,
  },
});

export default VerificationDetail;
