import {Dimensions, Platform, StyleSheet} from 'react-native';
import {FontFamily} from '@config/typography';
import {BaseColors} from '@config/theme';

const {width, height} = Dimensions.get('window');
const IOS = Platform.OS === 'ios';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseColors.white,
    paddingBottom: 20,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  scrollContainer: {
    flexGrow: 1,
  },
  laterTxtView: {
    alignSelf: 'center',
    marginBottom: height * 0.04,
    marginTop: 15,
  },
  laterTxtSty: {
    color: BaseColors.primary,
    fontSize: width * 0.05,
    fontFamily: FontFamily.OpenSansRegular,
  },
  imageContainer: {
    width: width / 1,
    height: height / 3.2,
    alignSelf: 'center',
    marginTop: Dimensions.get('screen').height / 15,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  headingText: {
    fontSize: 26,
    color: BaseColors.textBlack,
    textAlign: 'center',
    fontFamily: FontFamily.OpenSansSemiBold,
    // marginTop: 15,
  },
  bottomText: {
    fontSize: 20,
    color: BaseColors.lightTxtColor,
    textAlign: 'center',
    fontFamily: FontFamily.OpenSansMedium,
    marginBottom: 15,
  },
  descriptionText: {
    fontSize: 16,
    fontWeight: '400',
    color: BaseColors.logoColor,
    textAlign: 'center',
    marginVertical: 10,
    marginBottom: 20,
  },
  inputTitle: {
    color: BaseColors.inputColor,
  },
  textInput: {
    color: BaseColors.inputColor,
  },
  textInputContainer: {
    borderColor: BaseColors.primary,
    borderWidth: 2,
  },
  buttonContainer: {
    marginHorizontal: 15,
    marginBottom: 10,
    marginTop: 25,
  },
  button: {
    backgroundColor: BaseColors.primary,
  },
  lNameViewSty: {
    marginTop: IOS ? 20 : 15,
  },
  mainViewSty: {
    justifyContent: 'space-between',
    // flex: 1,
  },
  titleContainerSty: {
    marginBottom: 10,
  },
  mView: {
    // flex: 1,
    flexWrap: 'wrap',
  },
  txtaggrementView: {
    flexDirection: 'row',
    flexWrap: 'wrap', // Allow text to wrap to the next line
    alignItems: 'center', // Vertically align items
  },
  txtnavigationSty: {
    color: BaseColors.primary,
    borderBottomColor: BaseColors.primary,
    borderBottomWidth: 1,
    // textAlign: 'center',
    // backgroundColor: 'pink',
  },
  checkboxView: {
    alignContent: 'center',
    // justifyContent: 'center',
    flexDirection: 'row',
    // marginHorizontal: 15,
    marginTop: 30,
  },
  clickSty: {
    borderWidth: 1,
    borderColor: BaseColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    height: 17,
    width: 17,
    marginTop: 2,
  },
  errView: {
    paddingTop: 6,
  },
  checkboxErr: {
    fontSize: 14,
    color: BaseColors.red,
    fontFamily: FontFamily.OpenSansRegular,
    // alignSelf: 'center',
    // marginHorizontal: 35,
  },
  termsText: {
    color: BaseColors.textColor,
    fontFamily: FontFamily.OpenSansRegular,
    fontSize: 14,
  },
  margin: {
    marginLeft: 6,
    flexWrap: 'wrap',
  },
  secondView: {
    marginTop: 10,
  },
});
