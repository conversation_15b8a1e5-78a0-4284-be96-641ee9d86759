import React, { cloneElement, isValidElement, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Image, Animated } from 'react-native';
import styles from './styles';
import { BaseColors } from '@config/theme';
import { useAppDispatch } from '@components/UseRedux';
import userConfigActions from '@redux/reducers/userConfig/actions';

interface TabItem {
  key: string;
  label: string;
  icon?: React.ReactNode | string;
  activeIcon?: React.ReactNode | string;
  badgeCount?: number;
}

interface TabComponentProps {
  tabs: TabItem[];
  selectedTab: string;
  setSelectedTab: (key: string) => void;
  loader?: boolean;
  blinkingTabKey?: string;
  gallery?: boolean;
}

const TabComponent: React.FC<TabComponentProps> = ({
  tabs,
  selectedTab,
  setSelectedTab,
  loader,
  blinkingTabKey,
  gallery = false,
}) => {
  const blinkAnim = useRef(new Animated.Value(0)).current;
  const dispatch = useAppDispatch();
  const { setGalleryBlinking } = userConfigActions;

  useEffect(() => {
    if (blinkingTabKey) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(blinkAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: false,
          }),
          Animated.timing(blinkAnim, {
            toValue: 0,
            duration: 500,
            useNativeDriver: false,
          }),
        ])
      ).start();
    }
  }, [blinkingTabKey]);

  const interpolatedBgColor = blinkAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['transparent', BaseColors.primary], // transparent to primary color
  });

  const interpolatedColor = blinkAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [BaseColors.primary, BaseColors.primary], // transparent to primary color
  });

  return (
    <View style={[styles.container, {
      marginTop: gallery ? 15 : 0,
    }]}>
      <View style={styles.tabs}>
        {tabs.map((tab, index) => {
          const isBlinking = tab.key === blinkingTabKey;
          const isSelected = selectedTab === tab.key;

          const tint = isBlinking
            ? BaseColors.white
            : isSelected
              ? BaseColors.primary
              : '#7a7a7a';
          const TabWrapper = isBlinking ? Animated.View : View;
          const tabBackgroundStyle = isBlinking
            ? { backgroundColor: interpolatedBgColor, color: interpolatedColor, borderRadius: 6, padding: 8, minWidth: 80, justifyContent: 'center' }
            : { borderRadius: 6, padding: 8, minWidth: 80, justifyContent: 'center'};

          return (
            <TouchableOpacity
              key={tab.key}
              disabled={loader}
              onPress={() =>{
                dispatch(setGalleryBlinking(false) as any);
                setSelectedTab(tab.key);
              }}
              style={[styles.tab, { position: 'relative' }]}
            >
                <TabWrapper style={[tabBackgroundStyle, { flexDirection: 'row', alignItems: 'center'}]}>
                {tab.icon &&
                  (typeof tab.icon === 'string' ? (
                    <Image
                      source={{ uri: tab.icon }}
                      style={{ width: 18, height: 18, marginRight: 6, tintColor: isSelected ? BaseColors.white : BaseColors.primary }}
                      resizeMode="contain"
                    />
                  ) : isValidElement(tab.icon) ? (
                    <View style={{}}>

                      {cloneElement(
                        isSelected
                          ? tab.activeIcon || tab.icon
                          : tab.icon,
                        {
                          color: tint
                        }
                      )}
                    </View>
                  ) : null)}

</TabWrapper>
                {!gallery && (
                  <Text
                    style={[
                      styles.tabText,
                      isSelected && styles.activeText,
                      isBlinking && { color: BaseColors.white }, // Force white when blinking
                    ]}
                    >
                    {tab.label}
                  </Text>)}
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Tab underline indicator */}
      <View style={{ ...styles.barContainer, height: gallery ? 1 : 4 }}>
        <View style={{ ...styles.backgroundBar, height: gallery ? 1 : 8 }} />
        <View
          style={[
            styles.activeBar,
            {
              height: gallery ? 2 : 8,
              left: `${(tabs.findIndex((t) => t.key === selectedTab) / tabs.length) * 100}%`,
            },
          ]}
        />
      </View>
    </View>
  );
};

export default TabComponent;
