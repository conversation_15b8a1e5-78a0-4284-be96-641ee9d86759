import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface Props { note: string; numberOfLines?: number }

const ExpandableText = ({ note, numberOfLines = 4 }: Props) => {
  const [isTruncated, setIsTruncated] = useState(true);

  const toggleText = () => {
    setIsTruncated(!isTruncated);
  };

  if (!note) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.descriptionTxt} numberOfLines={isTruncated ? numberOfLines : undefined}>
        {note}
      </Text>
      {note.length > numberOfLines * 25 && ( // Approximate threshold for showing toggle
        <TouchableOpacity onPress={toggleText}>
          <Text style={[styles.toggleText, { marginTop: 3 }]}>
            {isTruncated ? 'Expand' : 'Collapse'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
  },
  descriptionTxt: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    lineHeight: 20,
  },
  toggleText: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.primary,
  },
});

export default ExpandableText;
