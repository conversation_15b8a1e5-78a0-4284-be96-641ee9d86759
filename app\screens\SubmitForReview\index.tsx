import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  UIManager,
  LayoutAnimation,
  ActivityIndicator,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Toast from 'react-native-simple-toast';
import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';
import Header from '@components/Header';
import TextInput from '@components/UI/TextInput'; // your CInput
import Button from '@components/UI/Button';
import { translate } from '@language/Translate';
import { ImagePickerModal } from '@components/SelectModal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getApiData } from '@app/utils/apiHelper';
import FastImage from 'react-native-fast-image';
import { endpoints } from '@config/endPoints';
import { isIOS, maxFileSize } from '@app/utils/CommonFunction';

// enable layout animation on android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const MAX_IMAGES = 10;
interface PayloadProps {
  note: string;
  media_proof: string[];
  requested_amount: number | null;
  requested_amount_desc: string;
  job_id: string | number;
  job_finish_request_id?: string | number;
}

type FormValues = {
  note: string;
  amount: string;
  desc: string;
};

const SubmitJobScreen: React.FC<{ navigation?: any, route?: any }> = ({ navigation, route }) => {
  const { params } = route;
  console.log('params: ', params);
  const initialValues = params?.initialValues || {};
  const isEdit = params?.edit || false;
  const insets = useSafeAreaInsets();
  const [expandMedia, setExpandMedia] = useState<boolean>(false);
  const [expandPayment, setExpandPayment] = useState<boolean>(false);
  const [images, setImages] = useState<string[]>([]);
  const [showPicker, setShowPicker] = useState<boolean>(false);
  const [galleryLoading, setGalleryLoading] = useState(false);
  const [imageRemoveLoader, setImageRemoveLoader] = useState<number | null>(null);
  console.log('images: ', images);


  // yup schema: only note enforced here (we do conditional checks for amount/desc in onSubmit)
  const schema = yup.object().shape({
    note: yup
      .string()
      .required(translate('submitJob.noteRequired'))
      .min(5, translate('submitJob.noteMin')),
  // amount/desc will be validated conditionally in onSubmit (so that expandPayment local state is respected)
  });

  const {
    control,
    handleSubmit,
    setError,
    setValue,
    clearErrors,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    mode: 'onSubmit', // validate only on submit (for note + others handled manually)
    defaultValues: {
      note: isEdit ? initialValues?.note : '',
      amount: isEdit ? String(initialValues?.requested_amount || '') : '',
      desc: isEdit ? initialValues?.requested_amount_desc : '',
    },
  });

  const [loader, setLoader] = useState(false);

  useEffect(() => {
    // To set initial values if in edit mode
    if (isEdit) {
      setImages(initialValues?.media_proof || []);
    }
  }, [isEdit]);


  // toggle collapse with layout animation and clear conditional errors when closing payment section
  const toggleSection = (type: 'media' | 'payment') => {
    LayoutAnimation.easeInEaseOut();
    if (type === 'media') {
      setExpandMedia(prev => !prev);
    } else {
      setExpandPayment(prev => {
        const next = !prev;
        if (!next) {
          // if closing payment section, clear related errors
          clearErrors(['amount', 'desc']);
        }
        return next;
      });
    }
  };

  const handleUpload = async (files: any) => {
    console.log('files: ', files);
    setGalleryLoading(true);
    const fileArray = Array.isArray(files) ? files : [files];

    try {
      for (const file of fileArray) {
        if (file.size > maxFileSize) {
          Toast.show(translate('fileSizeExceeded'), Toast.LONG);
          return;
        }
        const data = {
          file: {
            uri: file?.uri || file?.path,
            type: file?.mime || file.type || 'image/jpeg',
            name: file?.filename || file.name || 'photo.jpg',
          },
        };

        const res = await getApiData({
          endpoint: endpoints.uploadWorkProof,
          method: 'POST',
          data: data,
          formData: true,
        });

        if (res.status) {
          const newLocalURL = {
            type: 'local',
            ...res.data,
            url: res.data.filePath,
            id: Math.random(),
          };
          console.log('newLocalURL: ', images, newLocalURL);

          setImages(prev => [newLocalURL, ...prev]); // ✅ FIXED
          Toast.show(res?.message, Toast.BOTTOM);
        } else {
          Toast.show(res?.message || translate('error'), Toast.BOTTOM);
        }
      }
    } catch (err) {
      Toast.show(err?.message || translate('error'), Toast.BOTTOM);
    } finally {
      setGalleryLoading(false);
    }
  };

  const handleImageResult = (res: any) => {
    if (!res) {return;}
    if (images.length >= MAX_IMAGES) {return;}

    // ImageCropPicker returns array or object; DocumentPicker different — we expect images.path
    // const newPaths: string[] = Array.isArray(res.data)
    //   ? res.data.map((it: any) => it.path || it.uri)
    //   : [(res.data && (res.data.path || res.data.uri)) || (res.path || res.uri)];

    // const filtered = newPaths.filter(Boolean);
    handleUpload(res?.data);
    // setImages(prev => [...prev, ...filtered].slice(0, MAX_IMAGES));
  };

  const removeImageAt = async (index: number) => {
    if (images[index]?.type === 'local') {
      setImages(prev => prev.filter((_, i) => i !== index));
      return;
    }
    try {
      setImageRemoveLoader(index);
      const res = await getApiData({
        endpoint: endpoints.removeWorkProof,
        method: 'POST',
        data: { media_id: images[index]?.id, job_finish_request_id: initialValues?.job_finish_request_id },
      });
      if (res.status) {
        setImages(prev => prev.filter((_, i) => i !== index));
      } else {
        Toast.show(res?.message || translate('error'), Toast.BOTTOM);
      }
      setImageRemoveLoader(null);
    } catch (err) {
      setImageRemoveLoader(null);
      Toast.show(err?.message || translate('error'), Toast.BOTTOM);
    }
  };

  const submitPayload = async (payload: any) => {
    try {
      const res = await getApiData({
        endpoint: isEdit ? endpoints.updateFinishCounter : endpoints.submitJobForReview,
        method: 'POST',
        data: payload,
      });
      if (res?.status) {
        navigation?.goBack?.();
        Toast.show(res?.message, Toast.BOTTOM);
      } else {
        Toast.show(res?.message || translate('error'), Toast.BOTTOM);
      }
    } catch (err) {
      Toast.show(err?.message || translate('error'), Toast.BOTTOM);
    } finally {
      setLoader(false);
    }
  };

  const onSubmit = (data: FormValues) => {
    // Conditional validation for amount & desc when expandPayment is true
    setLoader(true);

    // Passed validation
    const payload: PayloadProps = {
      note: data.note,
      media_proof: images?.filter((file: any) => file?.fileName).map((img: any) => img?.fileName),
      requested_amount: Number(data.amount) > 0 ? Number(data.amount) : null ,
      requested_amount_desc: data.desc,
      job_id: params?.jobDetail?.jobId || params?.jobDetail?.id,
    };
    if (isEdit) {
      payload.job_finish_request_id = initialValues?.job_finish_request_id;
      if (images?.findIndex((img: any) => img?.type === 'local') === -1) {
        delete payload.media_proof;
      } else {
        payload.media_proof = images?.filter((img: any) => img?.type === 'local').map((img: any) => img?.fileName);
      }
    }

    submitPayload(payload);
    // TODO: send payload to API
    console.log('submit payload', payload);
    // navigate back or show success
    // navigation?.goBack?.();
  };

  return (
    <View style={[styles.container, { paddingBottom: isIOS() ? insets.bottom : insets.bottom * 2 }]}>
      <Header
        leftIcon="back-arrow"
        title={translate('submitJobForReview')}
        onLeftPress={() => navigation?.goBack?.()}
      />
      <View style={{ flex: 1, }}>
        <KeyboardAwareScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          enableOnAndroid
          extraScrollHeight={Platform.OS === 'android' ? 120 : 100}
          showsVerticalScrollIndicator={false}
        >
          {/* Note Field */}
          <Controller
            control={control}
            name="note"
            render={({ field: { onChange, value } }) => (
              <TextInput
                title={translate('note')}
                placeholderText={translate('typeHere')}
                textArea
                value={value}
                onChange={onChange}
                showError={!!errors.note}
                errorText={errors.note?.message as string}
              />
            )}
          />

          {/* Collapsible: Add Media */}
          <View style={styles.card}>
            <TouchableOpacity style={styles.header} onPress={() => toggleSection('media')}>
              <Ionicons
                name={expandMedia ? 'remove' : 'add'}
                size={18}
                color={BaseColors.primary}
              />
              <Text style={styles.headerText}>{translate('addMedia')}</Text>
            </TouchableOpacity>

            {expandMedia && (
              <View style={styles.content}>
                <Text style={styles.subLabel}>{translate('imagesHint')}</Text>
                <View style={styles.imageGrid}>
                  {images.length < MAX_IMAGES && (
                    <TouchableOpacity
                      style={styles.imageBox}
                      onPress={() => setShowPicker(true)}
                      activeOpacity={0.8}
                    >
                      {galleryLoading ? (
                        <ActivityIndicator size="small" color={BaseColors.primary} />
                      ) : (
                        <>
                          <Ionicons
                            name="cloud-upload-outline"
                            size={22}
                            color={BaseColors.primary}
                          />
                          <Text style={styles.chooseText}>{translate('chooseFiles')}</Text>
                        </>
                      )}
                    </TouchableOpacity>
                  )}

                  {images.map((image: any, idx) => (
                    <View key={idx} style={styles.imageBox}>
                      <FastImage source={{ uri: image?.url || image?.filePath || image, priority: FastImage.priority.high }} style={styles.imagePreview} />
                      <TouchableOpacity
                        style={styles.removeBtn}
                        onPress={() => removeImageAt(idx)}
                        activeOpacity={0.8}
                      >
                        {
                          imageRemoveLoader === idx ? (
                            <ActivityIndicator size="small" color="#fff" />
                          ) : (
                            <Ionicons name="close-circle" size={18} color="#fff" />
                          )
                        }
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              </View>
            )}

          </View>

          {/* Collapsible: Request Additional Payment */}
          <View style={styles.card}>
            <TouchableOpacity style={styles.header} onPress={() => toggleSection('payment')}>
              <Ionicons
                name={expandPayment ? 'remove' : 'add'}
                size={18}
                color={BaseColors.primary}
              />
              <Text style={styles.headerText}>{translate('requestAdditionalPayment')}</Text>
            </TouchableOpacity>

            {expandPayment && (
              <View style={styles.content}>
                <Controller
                  control={control}
                  name="amount"
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      title={translate('amount')}
                      placeholderText={translate('typeHere')}
                      keyBoardType="numeric"
                      value={value ?? ''}
                      // ensure only digits and max 5 chars
                      onChange={(text: string) => {
                        let digits = text.replace(/[^0-9]/g, '');
                        if (digits.length > 5) {digits = digits.slice(0, 5);}
                        onChange(digits);
                        if (errors.amount) {clearErrors('amount');}
                      }}
                      showError={!!errors.amount}
                      errorText={errors.amount?.message as string}
                    />
                  )}
                />

                <Controller
                  control={control}
                  name="desc"
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      title={translate('description')}
                      placeholderText={translate('typeHere')}
                      textArea
                      value={value ?? ''}
                      onChange={(txt: string) => {
                        onChange(txt);
                        if (errors.desc) {clearErrors('desc');}
                      }}
                      showError={!!errors.desc}
                      errorText={errors.desc?.message as string}
                    />
                  )}
                />
              </View>
            )}
          </View>
        </KeyboardAwareScrollView>
      </View>
      {/* Footer Buttons */}
      <View style={[styles.footer]}>
        <View style={styles.footerRow}>
          <View style={styles.flexOne}>
            <Button type="outlined" onPress={() => navigation?.goBack?.()}>
              {translate('cancel')}
            </Button>
          </View>

          <View style={[styles.flexOne, { marginLeft: 8 }]}>
            <Button loading={loader} onPress={handleSubmit(onSubmit)}>{translate('submit')}</Button>
          </View>
        </View>
      </View>

      {/* Image Picker Modal */}
      <ImagePickerModal
        visible={showPicker}
        onClose={() => setShowPicker(false)}
        onResult={handleImageResult}
      />
    </View>
  );
};

export default SubmitJobScreen;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: BaseColors.white },
  scrollContent: { padding: 16, marginHorizontal: 5 },
  card: {
    backgroundColor: BaseColors.white,
    borderRadius: 10,
    marginTop: 16,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: BaseColors.borderColor,
    overflow: 'hidden',
  },

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
  },
  headerText: {
    marginLeft: 8,
    fontSize: 15,
    fontFamily: FontFamily.OpenSansSemiBold,
    color: BaseColors.textBlack,
  },

  content: {
    paddingHorizontal: 16,
    paddingBottom: 12,
    gap: 12,
  },

  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10, // works in RN 0.71+, otherwise use margin
  },
  subLabel: {
    fontSize: 13,
    color: BaseColors.textGrey,
    fontFamily: FontFamily.OpenSansItalic,
    marginBottom: 8,
  },
  imageRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageBox: {
    width: 90,
    height: 90,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: BaseColors.borderColor,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: BaseColors.inputBackground,
    marginRight: 10,
  },
  chooseText: {
    fontSize: 12,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.primary,
    textAlign: 'center',
    marginTop: 6,
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
  removeBtn: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: BaseColors.red,
    borderRadius: 12,
    padding: 2,
  },
  footer: {
    flex: 0.1,
    marginHorizontal: 20,
    backgroundColor: BaseColors.white,
  },
  footerRow: { flexDirection: 'row', alignItems: 'center' },
  flexOne: { flex: 1 },
});
