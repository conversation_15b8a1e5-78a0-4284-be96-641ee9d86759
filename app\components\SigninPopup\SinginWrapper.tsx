import React from 'react';
import SignInModal from '.';
import { useAppDispatch, useAppSelector } from '@components/UseRedux';
import authActions from '@redux/reducers/auth/actions';

export default function SingInWrapper() {
  const dispatch = useAppDispatch();
  const { loginPopup } = useAppSelector((state: any) => state.auth);
  return (
    <SignInModal visible={loginPopup} onClose={() => dispatch(authActions.setLoginPopupOpen(false) as any)} />
  );
}
