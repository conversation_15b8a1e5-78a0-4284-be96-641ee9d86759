// usePaymentSheet.ts
import { useState, useCallback } from 'react';
import {
  initPaymentSheet,
  presentPaymentSheet,
} from '@stripe/stripe-react-native';

interface InitOptions {
  paymentIntentClientSecret: string;
  customerId: string;
  customerEphemeralKeySecret: string;
}

interface UsePaymentSheetOptions {
  onSuccess?: () => void;
  onError?: (error: any) => void;
}

export function usePaymentSheet({ onSuccess, onError }: UsePaymentSheetOptions) {
  const [loading, setLoading] = useState(false);

  const initializePaymentSheet = useCallback(
    async ({ paymentIntentClientSecret, customerId, customerEphemeralKeySecret }: InitOptions) => {
      try {
        setLoading(true);

        const { error } = await initPaymentSheet({
          merchantDisplayName: 'The Harbor App',
          customerId,
          customerEphemeralKeySecret,
          paymentIntentClientSecret,
          allowsDelayedPaymentMethods: true,
          googlePay: {
            merchantCountryCode: 'US',
            testEnv: false,
          },
          applePay: {
            merchantCountryCode: 'US',
          },
          returnURL: 'com.harbor.newapp://stripe-redirect',
        });

        if (error) throw error;
      } catch (err) {
        onError?.(err);
      } finally {
        setLoading(false);
      }
    },
    [onError]
  );

  const openPaymentSheet = useCallback(
    async (delayMs = 0) => {
      try {
        setLoading(true);

        if (delayMs > 0) {
          await new Promise((resolve) => setTimeout(resolve, delayMs));
        }

        const { error } = await presentPaymentSheet();
        if (error) throw error;

        onSuccess?.();
      } catch (err) {
        onError?.(err);
      } finally {
        setLoading(false);
      }
    },
    [onSuccess, onError]
  );

  return { initializePaymentSheet, openPaymentSheet, loading };
}
