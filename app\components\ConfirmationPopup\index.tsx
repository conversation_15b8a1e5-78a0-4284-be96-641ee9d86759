import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { BaseColors } from '@config/theme';
import { FontFamily } from '@config/typography';

interface ConfirmationModalProps {
  visible: boolean;
  title: string;
  subtitle?: string;
  onCancel: () => void;
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
  loading?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  visible,
  title,
  subtitle,
  onCancel,
  onConfirm,
  confirmText = 'Yes, Delete',
  cancelText = 'Cancel',
  loading = false,
}) => {
  return (
    <Modal
      animationType="fade"
      transparent
      visible={visible}
      onRequestClose={onCancel}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Text style={styles.title}>{title}</Text>
          {subtitle ? <Text style={styles.subtitle}>{subtitle}</Text> : null}

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onCancel}
              activeOpacity={0.8}>
              <Text style={styles.cancelText}>{cancelText}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={onConfirm}
              disabled={loading}
              activeOpacity={0.8}>
              <Text style={styles.confirmText}>
                {loading ? 'Deleting...' : confirmText}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: '80%',
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontFamily: FontFamily.OpenSansBold,
    color: BaseColors.textBlack,
    textAlign: 'left',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansRegular,
    color: BaseColors.textGrey,
    textAlign: 'left',
    marginTop: 8,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 20,
  },
  button: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 6,
  },
  cancelButton: {
    backgroundColor: BaseColors.white,
    marginRight: 10,
    borderWidth: 1,
    borderColor: BaseColors.borderColor,
  },
  confirmButton: {
    backgroundColor: BaseColors.white,
    borderWidth: 1,
    borderColor: BaseColors.red,
  },
  cancelText: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansSemiBold,
    color: BaseColors.textBlack,
  },
  confirmText: {
    fontSize: 14,
    fontFamily: FontFamily.OpenSansSemiBold,
    color: BaseColors.red,
  },
});

export default ConfirmationModal;
